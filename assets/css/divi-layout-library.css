/* Divi Layout Library Styles */

/* Keyframes */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Main App Container */
.dll-app {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: #23282d;
    line-height: 1.4;
}

/* Dashboard */
.dll-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.dll-dashboard--loading,
.dll-dashboard--error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.dll-dashboard__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.dll-dashboard__title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #23282d;
}

.dll-dashboard__toolbar {
    display: flex;
    align-items: center;
    gap: 15px;
}

.dll-dashboard__content {
    display: flex;
    gap: 30px;
}

.dll-dashboard__main {
    flex: 1;
}

/* View Toggle */
.dll-view-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 3px;
    overflow: hidden;
}

.dll-view-toggle__button {
    background: #fff;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
}

.dll-view-toggle__button:hover {
    background-color: #f9f9f9;
}

.dll-view-toggle__button--active {
    background-color: #0073aa;
    color: #fff;
}

.dll-view-toggle__button .dashicons {
    font-size: 16px;
}

/* Buttons */
.dll-button {
    background-color: #0073aa;
    color: #fff;
    border: 1px solid #005a87;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s ease;
}

.dll-button:hover {
    background-color: #005a87;
    border-color: #004a73;
    color: #fff;
}

.dll-button:focus {
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #0073aa;
    outline: none;
}

.dll-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.dll-button--secondary {
    background-color: #fff;
    color: #23282d;
    border-color: #ccc;
}

.dll-button--secondary:hover {
    background-color: #f6f7f7;
    border-color: #999;
    color: #23282d;
}

.dll-button--small {
    padding: 6px 12px;
    font-size: 12px;
}

.dll-button .dashicons {
    font-size: 14px;
}

/* Loading */
.dll-loading {
    text-align: center;
}

.dll-loading__spinner {
    border: 2px solid #e5e5e5;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    width: 40px;
    height: 40px;
    margin: 0 auto 15px;
}

.dll-loading__spinner--small {
    width: 16px;
    height: 16px;
    border-width: 1px;
}

/* Error */
.dll-error {
    text-align: center;
    padding: 40px 20px;
}

.dll-error h3 {
    color: #dc3232;
    margin-bottom: 10px;
}

.dll-error p {
    margin-bottom: 20px;
    color: #666;
}

/* Sidebar */
.dll-sidebar {
    width: 250px;
    flex-shrink: 0;
}

.dll-sidebar__header {
    margin-bottom: 20px;
}

.dll-sidebar__title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #23282d;
}

.dll-sidebar__title .dashicons {
    color: #0073aa;
}

/* Category List */
.dll-category-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dll-category-list__item {
    margin-bottom: 2px;
}

.dll-category-list__button {
    width: 100%;
    background: none;
    border: none;
    padding: 10px 15px;
    text-align: left;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dll-category-list__button:hover {
    background-color: #f9f9f9;
}

.dll-category-list__button--active {
    background-color: #0073aa;
    color: #fff;
}

.dll-category-list__name {
    font-weight: 500;
}

.dll-category-list__count {
    background-color: #e5e5e5;
    color: #666;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.dll-category-list__button--active .dll-category-list__count {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* Layouts Grid/List */
.dll-layouts--grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.dll-layouts--list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dll-layouts__empty {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    grid-column: 1 / -1;
}

/* Layout Card */
.dll-layout-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dll-layout-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dll-layout-card--grid .dll-layout-card__image-container {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.dll-layout-card--grid .dll-layout-card__content {
    padding: 15px;
}

.dll-layout-card--list {
    display: flex;
    align-items: center;
}

.dll-layout-card--list .dll-layout-card__image-wrapper {
    width: 150px;
    flex-shrink: 0;
}

.dll-layout-card--list .dll-layout-card__image-container {
    height: 100px;
    position: relative;
    overflow: hidden;
}

.dll-layout-card--list .dll-layout-card__content-wrapper {
    flex: 1;
    padding: 15px;
}

.dll-layout-card--list .dll-layout-card__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dll-layout-card--list .dll-layout-card__actions-list {
    margin-left: 20px;
}

.dll-layout-card__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.dll-layout-card__image--scrolling {
    transform: translateY(-20%);
}

.dll-layout-card__image-placeholder,
.dll-layout-card__image-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f9f9f9;
    color: #666;
    flex-direction: column;
    gap: 10px;
}

.dll-layout-card__image-placeholder .dashicons,
.dll-layout-card__image-error .dashicons {
    font-size: 24px;
}

.dll-layout-card__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dll-layout-card:hover .dll-layout-card__overlay {
    opacity: 1;
}

.dll-layout-card__actions {
    display: flex;
    gap: 10px;
}

.dll-layout-card__title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #23282d;
}

.dll-layout-card__category {
    font-size: 12px;
    color: #0073aa;
    font-weight: 500;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dll-layout-card__description {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Modal */
.dll-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
    padding: 20px;
}

.dll-modal {
    background: #fff;
    border-radius: 6px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dll-modal__header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9f9f9;
}

.dll-modal__title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dll-modal__title .dashicons {
    color: #0073aa;
}

.dll-modal__close {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    border-radius: 3px;
    color: #666;
}

.dll-modal__close:hover {
    background-color: #e5e5e5;
}

.dll-modal__close .dashicons {
    font-size: 18px;
}

.dll-modal__content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.dll-modal__footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f9f9f9;
}

/* Forms */
.dll-form-group {
    margin-bottom: 20px;
}

.dll-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #23282d;
}

.dll-form-input,
.dll-form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.dll-form-input:focus,
.dll-form-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.dll-form-help {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* Radio Options */
.dll-radio-option {
    display: block;
    margin-bottom: 15px;
    cursor: pointer;
}

.dll-radio-option input[type="radio"] {
    margin-right: 10px;
}

.dll-radio-option__label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

/* Import Modal Specific */
.dll-import-modal .dll-import-options {
    margin-bottom: 25px;
}

.dll-import-modal .dll-import-options h3 {
    margin-bottom: 15px;
    font-size: 16px;
}

.dll-import-modal .dll-page-options {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 3px;
    margin-top: 15px;
}

.dll-import-progress {
    text-align: center;
    padding: 40px 20px;
}

.dll-import-progress__text {
    margin-bottom: 20px;
    font-size: 16px;
    color: #23282d;
}

.dll-progress {
    margin-bottom: 15px;
}

.dll-progress__bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
}

.dll-progress__fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.dll-progress__text {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #23282d;
}

.dll-import-success {
    text-align: center;
    padding: 40px 20px;
    position: relative;
}

.dll-import-success__icon {
    font-size: 48px;
    color: #46b450;
    margin-bottom: 15px;
}

.dll-import-success__icon .dashicons {
    font-size: 48px;
}

.dll-import-success__title {
    font-size: 20px;
    margin-bottom: 10px;
    color: #23282d;
}

.dll-import-success__message {
    margin-bottom: 25px;
    color: #666;
}

.dll-import-success__actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.dll-import-error {
    text-align: center;
    padding: 40px 20px;
}

.dll-import-error__icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.dll-import-error__title {
    font-size: 18px;
    color: #dc3232;
    margin-bottom: 10px;
}

.dll-import-error__message {
    margin-bottom: 25px;
    color: #666;
}

/* Export Modal Specific */
.dll-export-modal {
    max-width: 700px;
}

.dll-export-step {
    margin-bottom: 30px;
}

.dll-export-step h3 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #23282d;
}

.dll-layout-list__empty {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.dll-layout-list__items {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.dll-layout-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dll-layout-item:last-child {
    border-bottom: none;
}

.dll-layout-item:hover {
    background-color: #f9f9f9;
}

.dll-layout-item--selected {
    background-color: #e6f3ff;
    border-color: #0073aa;
}

.dll-layout-item__content {
    flex: 1;
}

.dll-layout-item__title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.dll-layout-item__meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.dll-layout-item__type,
.dll-layout-item__status {
    text-transform: capitalize;
}

.dll-layout-item__actions {
    margin-left: 15px;
}

.dll-layout-item__edit {
    color: #666;
    text-decoration: none;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.dll-layout-item__edit:hover {
    background-color: #e5e5e5;
    color: #0073aa;
}

.dll-export-success {
    text-align: center;
    padding: 40px 20px;
}

.dll-export-success__icon {
    font-size: 48px;
    color: #46b450;
    margin-bottom: 15px;
}

.dll-export-success__icon .dashicons {
    font-size: 48px;
}

.dll-export-success h3 {
    color: #46b450;
    margin-bottom: 10px;
}

.dll-export-error-inline {
    background-color: #ffeaea;
    color: #dc3232;
    padding: 10px 15px;
    border-radius: 3px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dll-export-error-inline .dashicons {
    flex-shrink: 0;
}

/* Confetti Animation */
.dll-confetti {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.dll-confetti__piece {
    position: absolute;
    width: 8px;
    height: 8px;
    top: -10px;
    animation: confetti-fall 3s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dll-dashboard {
        padding: 15px;
    }

    .dll-dashboard__header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .dll-dashboard__toolbar {
        width: 100%;
        justify-content: space-between;
    }

    .dll-dashboard__content {
        flex-direction: column;
        gap: 20px;
    }

    .dll-sidebar {
        width: 100%;
    }

    .dll-layouts--grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .dll-layout-card--list {
        flex-direction: column;
    }

    .dll-layout-card--list .dll-layout-card__image-wrapper {
        width: 100%;
    }

    .dll-layout-card--list .dll-layout-card__content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .dll-layout-card--list .dll-layout-card__actions-list {
        margin-left: 0;
    }

    .dll-modal {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }

    .dll-modal__content {
        padding: 15px;
    }

    .dll-modal__footer {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .dll-modal__footer .dll-button {
        width: 100%;
        justify-content: center;
    }

    .dll-import-success__actions {
        flex-direction: column;
        gap: 10px;
    }

    .dll-import-success__actions .dll-button {
        width: 100%;
        justify-content: center;
    }
}
