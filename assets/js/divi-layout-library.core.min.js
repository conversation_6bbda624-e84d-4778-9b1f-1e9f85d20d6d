/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./react_app/App.jsx":
/*!***************************!*\
  !*** ./react_app/App.jsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/Dashboard */ "./react_app/components/Dashboard.jsx");



const App = () => {
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-app"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_1__["default"], null));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);

/***/ }),

/***/ "./react_app/components/Dashboard.jsx":
/*!********************************************!*\
  !*** ./react_app/components/Dashboard.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/ApiService */ "./react_app/services/ApiService.js");
/* harmony import */ var _LayoutCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LayoutCard */ "./react_app/components/LayoutCard.jsx");
/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ "./react_app/components/Sidebar.jsx");
/* harmony import */ var _ImportModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImportModal */ "./react_app/components/ImportModal.jsx");
/* harmony import */ var _ExportModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ExportModal */ "./react_app/components/ExportModal.jsx");







const {
  plugin_root_url
} = window.dllAjax;
const Dashboard = () => {
  const [layouts, setLayouts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);
  const [filteredLayouts, setFilteredLayouts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);
  const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('all');
  const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('grid'); // 'grid' or 'list'
  const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);
  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [showImportModal, setShowImportModal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [showExportModal, setShowExportModal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [selectedLayout, setSelectedLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const apiService = new _services_ApiService__WEBPACK_IMPORTED_MODULE_1__["default"]();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    loadPredefinedLayouts();
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    filterLayouts();
  }, [layouts, selectedCategory]);

  /**
   * Load predefined layouts from static data
   */
  const loadPredefinedLayouts = () => {
    try {
      setLoading(true);

      // Sample predefined layouts data
      const predefinedLayouts = [{
        id: 'layout-1',
        name: 'Modern Business',
        category: 'Business',
        description: 'A clean and modern business layout for corporate sites.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/business-modern.jpg',
        previewLink: 'https://demo.example.com/business-modern',
        jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Web-Services.json'
      }, {
        id: 'layout-2',
        name: 'Creative Portfolio',
        category: 'Portfolio',
        description: 'A creative portfolio layout perfect for showcasing work.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/portfolio-creative.jpg',
        previewLink: 'https://demo.example.com/portfolio-creative',
        jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/portfolio-creative.json'
      }, {
        id: 'layout-3',
        name: 'Restaurant Menu',
        category: 'Restaurant',
        description: 'An elegant restaurant layout with menu showcase.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/restaurant-menu.jpg',
        previewLink: 'https://demo.example.com/restaurant-menu',
        jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/restaurant-menu.json'
      }, {
        id: 'layout-4',
        name: 'Tech Startup',
        category: 'Business',
        description: 'A modern tech startup layout with bold design.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/tech-startup.jpg',
        previewLink: 'https://demo.example.com/tech-startup',
        jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/blurbcore.json'
      }, {
        id: 'layout-5',
        name: 'Photography Studio',
        category: 'Portfolio',
        description: 'A stunning photography portfolio layout.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/photography-studio.jpg',
        previewLink: 'https://demo.example.com/photography-studio',
        jsonFile: '/wp-content/plugins/divi-layout-library/assets/layouts/photography-studio.json'
      }, {
        id: 'layout-6',
        name: 'Coffee Shop',
        category: 'Restaurant',
        description: 'A cozy coffee shop layout with warm colors.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg',
        previewLink: 'https://demo.example.com/coffee-shop',
        jsonFile: '/wp-content/plugins/divi-layout-library/assets/layouts/coffee-shop.json'
      }, {
        id: 'layout-7',
        name: 'Gardener Shop',
        category: 'Garden',
        description: 'A garden shop.',
        previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg',
        previewLink: 'https://demo.example.com/coffee-shop',
        jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Gardener-All-Layouts-Import.json'
      }];
      setLayouts(predefinedLayouts);
      setLoading(false);
    } catch (err) {
      setError('Failed to load layouts');
      setLoading(false);
    }
  };

  /**
   * Filter layouts by selected category
   */
  const filterLayouts = () => {
    if (selectedCategory === 'all') {
      setFilteredLayouts(layouts);
    } else {
      setFilteredLayouts(layouts.filter(layout => layout.category === selectedCategory));
    }
  };

  /**
   * Get unique categories from layouts
   */
  const getCategories = () => {
    const categories = ['all'];
    layouts.forEach(layout => {
      if (!categories.includes(layout.category)) {
        categories.push(layout.category);
      }
    });
    return categories;
  };

  /**
   * Handle layout import
   */
  const handleImportLayout = layout => {
    setSelectedLayout(layout);
    setShowImportModal(true);
  };

  /**
   * Handle layout preview
   */
  const handlePreviewLayout = layout => {
    if (layout.previewLink) {
      window.open(layout.previewLink, '_blank');
    }
  };

  /**
   * Handle export button click
   */
  const handleExportClick = () => {
    setShowExportModal(true);
  };

  /**
   * Toggle view mode between grid and list
   */
  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };
  if (loading) {
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-dashboard dll-dashboard--loading"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-loading"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-loading__spinner"
    }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "Loading layouts...")));
  }
  if (error) {
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-dashboard dll-dashboard--error"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-error"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "Error"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, error), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
      onClick: loadPredefinedLayouts,
      className: "dll-button dll-button--primary"
    }, "Try Again")));
  }
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-dashboard"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-dashboard__header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h1", {
    className: "dll-dashboard__title"
  }, "Divi Layout Library"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-dashboard__toolbar"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-view-toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: `dll-view-toggle__button ${viewMode === 'grid' ? 'dll-view-toggle__button--active' : ''}`,
    onClick: () => setViewMode('grid'),
    title: "Grid View"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-grid-view"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: `dll-view-toggle__button ${viewMode === 'list' ? 'dll-view-toggle__button--active' : ''}`,
    onClick: () => setViewMode('list'),
    title: "List View"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-list-view"
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--secondary",
    onClick: handleExportClick
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-download"
  }), "Export Layout"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-dashboard__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__["default"], {
    categories: getCategories(),
    selectedCategory: selectedCategory,
    onCategoryChange: setSelectedCategory
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-dashboard__main"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `dll-layouts dll-layouts--${viewMode}`
  }, filteredLayouts.length === 0 ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layouts__empty"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "No layouts found for the selected category.")) : filteredLayouts.map(layout => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_LayoutCard__WEBPACK_IMPORTED_MODULE_2__["default"], {
    key: layout.id,
    layout: layout,
    viewMode: viewMode,
    onImport: () => handleImportLayout(layout),
    onPreview: () => handlePreviewLayout(layout)
  }))))), showImportModal && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ImportModal__WEBPACK_IMPORTED_MODULE_4__["default"], {
    layout: selectedLayout,
    onClose: () => {
      setShowImportModal(false);
      setSelectedLayout(null);
    }
  }), showExportModal && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ExportModal__WEBPACK_IMPORTED_MODULE_5__["default"], {
    onClose: () => setShowExportModal(false)
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);

/***/ }),

/***/ "./react_app/components/ExportModal.jsx":
/*!**********************************************!*\
  !*** ./react_app/components/ExportModal.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/ApiService */ "./react_app/services/ApiService.js");



const ExportModal = ({
  onClose
}) => {
  const [layouts, setLayouts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);
  const [selectedLayout, setSelectedLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [exportName, setExportName] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');
  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);
  const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [exportSuccess, setExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const apiService = new _services_ApiService__WEBPACK_IMPORTED_MODULE_1__["default"]();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    loadAvailableLayouts();
  }, []);

  /**
   * Load available layouts for export
   */
  const loadAvailableLayouts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await apiService.getAvailableLayouts();
      setLayouts(result.layouts || []);
    } catch (err) {
      setError(err.message || 'Failed to load layouts');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle layout selection
   */
  const handleLayoutSelect = layout => {
    setSelectedLayout(layout);
    setExportName(layout.title || '');
    setError(null);
  };

  /**
   * Handle export process
   */
  const handleExport = async () => {
    if (!selectedLayout) {
      setError(apiService.getString('selectLayout') || 'Please select a layout to export');
      return;
    }
    setIsExporting(true);
    setError(null);
    try {
      // Export the layout
      const result = await apiService.exportLayout(selectedLayout.id, exportName.trim() || selectedLayout.title);

      // Download the exported file
      const filename = exportName.trim() || selectedLayout.title || 'divi_layout';
      apiService.downloadLayoutFile(result.export_data, filename);

      // Show success state
      setExportSuccess(true);

      // Auto-close after 2 seconds
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      setError(err.message || 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Handle modal close
   */
  const handleClose = () => {
    if (!isExporting) {
      onClose();
    }
  };

  /**
   * Format date for display
   */
  const formatDate = dateString => {
    return new Date(dateString).toLocaleDateString();
  };

  /**
   * Render loading state
   */
  const renderLoading = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-loading"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-loading__spinner"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "Loading available layouts..."));

  /**
   * Render error state
   */
  const renderError = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-error"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-error__icon"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-warning"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "Error"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, error), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--primary",
    onClick: loadAvailableLayouts
  }, "Try Again"));

  /**
   * Render success state
   */
  const renderSuccess = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-success"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-success__icon"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-yes-alt"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "Export Successful!"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "Your layout has been downloaded successfully."));

  /**
   * Render layout list
   */
  const renderLayoutList = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-list"
  }, layouts.length === 0 ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-list__empty"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "No layouts available for export."), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "Create some pages with Divi Builder first.")) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-list__items"
  }, layouts.map(layout => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    key: layout.id,
    className: `dll-layout-item ${selectedLayout?.id === layout.id ? 'dll-layout-item--selected' : ''}`,
    onClick: () => handleLayoutSelect(layout)
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", {
    className: "dll-layout-item__title"
  }, layout.title || 'Untitled'), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-item__meta"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-layout-item__type"
  }, layout.type), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-layout-item__status"
  }, layout.status), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-layout-item__date"
  }, "Modified: ", formatDate(layout.modified)))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-item__actions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: layout.edit_url,
    className: "dll-layout-item__edit",
    target: "_blank",
    rel: "noopener noreferrer",
    onClick: e => e.stopPropagation(),
    title: "Edit this layout"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-edit"
  })))))));

  /**
   * Render export form
   */
  const renderExportForm = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-form"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-form-group"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "exportName",
    className: "dll-form-label"
  }, "Export Name (optional)"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "text",
    id: "exportName",
    className: "dll-form-input",
    value: exportName,
    onChange: e => setExportName(e.target.value),
    placeholder: "Enter custom name for export"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-form-help"
  }, "Leave empty to use the layout title as filename.")));
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal-overlay",
    onClick: handleClose
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal dll-export-modal",
    onClick: e => e.stopPropagation()
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h2", {
    className: "dll-modal__title"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-download"
  }), "Export Layout"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-modal__close",
    onClick: handleClose,
    disabled: isExporting
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-no-alt"
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__content"
  }, isLoading ? renderLoading() : error && !selectedLayout ? renderError() : exportSuccess ? renderSuccess() : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-step"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "1. Select Layout to Export"), renderLayoutList()), selectedLayout && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-step"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "2. Export Options"), renderExportForm()), error && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-export-error-inline"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-warning"
  }), error))), !isLoading && !exportSuccess && layouts.length > 0 && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__footer"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--secondary",
    onClick: handleClose,
    disabled: isExporting
  }, "Cancel"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--primary",
    onClick: handleExport,
    disabled: !selectedLayout || isExporting
  }, isExporting ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-loading__spinner dll-loading__spinner--small"
  }), apiService.getString('exporting')) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-download"
  }), "Export & Download")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExportModal);

/***/ }),

/***/ "./react_app/components/ImportModal.jsx":
/*!**********************************************!*\
  !*** ./react_app/components/ImportModal.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/ApiService */ "./react_app/services/ApiService.js");



const ImportModal = ({
  layout,
  onClose
}) => {
  const [importType, setImportType] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('library'); // 'library' or 'page'
  const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');
  const [pageStatus, setPageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('draft');
  const [isImporting, setIsImporting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);
  const [importResult, setImportResult] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const apiService = new _services_ApiService__WEBPACK_IMPORTED_MODULE_1__["default"]();

  /**
   * Handle import type change
   */
  const handleImportTypeChange = type => {
    setImportType(type);
    setError(null);

    // Set default page name when switching to page creation
    if (type === 'page' && !pageName) {
      setPageName(layout?.name || '');
    }
  };

  /**
   * Validate form inputs
   */
  const validateInputs = () => {
    if (importType === 'page') {
      if (!pageName.trim()) {
        setError(apiService.getString('pageNameRequired') || 'Page name is required');
        return false;
      }
    }
    return true;
  };

  /**
   * Simulate progress for better UX
   */
  const simulateProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + Math.random() * 20;
      });
    }, 200);
    return interval;
  };

  /**
   * Handle import process
   */
  const handleImport = async () => {
    if (!validateInputs()) {
      return;
    }
    setIsImporting(true);
    setError(null);
    setImportResult(null);
    const progressInterval = simulateProgress();
    try {
      // Load layout file from URL and convert to File object
      const response = await fetch(layout.jsonFile);
      if (!response.ok) {
        throw new Error('Failed to load layout file');
      }
      const jsonContent = await response.text();
      const fileName = layout.jsonFile.split('/').pop() || 'layout.json';
      const file = new File([jsonContent], fileName, {
        type: 'application/json'
      });

      // Import using Divi's native system
      const importOptions = {
        includeGlobalPresets: false,
        createPage: importType === 'page',
        pageTitle: importType === 'page' ? pageName.trim() : undefined
      };
      console.info(importOptions);
      const result = await apiService.importLayout(file, importOptions);

      // Complete progress
      clearInterval(progressInterval);
      setProgress(100);

      // Verify the import was successful
      if (result.success) {
        console.log('Import completed, verifying...');
        const verification = await apiService.verifyImportSuccess(result);
        console.log('Verification result:', verification);

        // Update result with verification info
        result.verification = verification;
      }

      // Show success state
      setImportResult(result);
      setShowConfetti(true);

      // Auto-hide confetti after 3 seconds
      setTimeout(() => {
        setShowConfetti(false);
      }, 3000);
    } catch (err) {
      clearInterval(progressInterval);
      setError(err.message || apiService.getString('error'));
      setProgress(0);
    } finally {
      setIsImporting(false);
    }
  };

  /**
   * Handle modal close
   */
  const handleClose = () => {
    if (!isImporting) {
      onClose();
    }
  };

  /**
   * Render progress bar
   */
  const renderProgressBar = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-progress"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-progress__bar"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-progress__fill",
    style: {
      width: `${progress}%`
    }
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-progress__text"
  }, progress < 100 ? `${Math.round(progress)}%` : 'Complete!'));

  /**
   * Render success state
   */
  const renderSuccess = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-success"
  }, showConfetti && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-confetti"
  }, Array.from({
    length: 50
  }).map((_, i) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    key: i,
    className: "dll-confetti__piece",
    style: {
      left: `${Math.random() * 100}%`,
      animationDelay: `${Math.random() * 3}s`,
      backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)]
    }
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-success__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-success__icon"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-yes-alt"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", {
    className: "dll-import-success__title"
  }, "Congratulations! \uD83C\uDF89"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-import-success__message"
  }, importType === 'page' ? `Page "${pageName}" has been created successfully!` : 'Layout has been imported to your library successfully!'), importResult?.data?.edit_url && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-success__actions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: importResult.data.edit_url,
    className: "dll-button dll-button--primary",
    target: "_blank",
    rel: "noopener noreferrer"
  }, "Edit Page"), importResult.data.view_url && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: importResult.data.view_url,
    className: "dll-button dll-button--secondary",
    target: "_blank",
    rel: "noopener noreferrer"
  }, "View Page"))));

  /**
   * Render error state
   */
  const renderError = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-error"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-error__icon"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, "\uD83D\uDE1E")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", {
    className: "dll-import-error__title"
  }, "Import Failed"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-import-error__message"
  }, error), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--primary",
    onClick: () => {
      setError(null);
      setProgress(0);
    }
  }, "Try Again"));
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal-overlay",
    onClick: handleClose
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal dll-import-modal",
    onClick: e => e.stopPropagation()
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h2", {
    className: "dll-modal__title"
  }, "Import Layout: ", layout?.name), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-modal__close",
    onClick: handleClose,
    disabled: isImporting
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-no-alt"
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__content"
  }, error ? renderError() : importResult ? renderSuccess() : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, isImporting ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-progress"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-import-progress__text"
  }, apiService.getString('importing')), renderProgressBar()) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-form"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-import-options"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", null, "Import Options"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    className: "dll-radio-option"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "radio",
    name: "importType",
    value: "page",
    checked: importType === 'page',
    onChange: () => handleImportTypeChange('page')
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-radio-option__label"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("strong", null, "Make a New Page"), " with this layout")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    className: "dll-radio-option"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "radio",
    name: "importType",
    value: "library",
    checked: importType === 'library',
    onChange: () => handleImportTypeChange('library')
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-radio-option__label"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("strong", null, "Just Import Layout"), " (add to Divi library)"))), importType === 'page' && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-page-options"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-form-group"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "pageName",
    className: "dll-form-label"
  }, "Page Name *"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "text",
    id: "pageName",
    className: "dll-form-input",
    value: pageName,
    onChange: e => setPageName(e.target.value),
    placeholder: "Enter page name",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-form-group"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "pageStatus",
    className: "dll-form-label"
  }, "Page Status"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("select", {
    id: "pageStatus",
    className: "dll-form-select",
    value: pageStatus,
    onChange: e => setPageStatus(e.target.value)
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("option", {
    value: "draft"
  }, "Draft"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("option", {
    value: "publish"
  }, "Published"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("option", {
    value: "private"
  }, "Private"))))))), !isImporting && !importResult && !error && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-modal__footer"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--secondary",
    onClick: handleClose
  }, "Cancel"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--primary",
    onClick: handleImport
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-download"
  }), "Import Layout"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImportModal);

/***/ }),

/***/ "./react_app/components/LayoutCard.jsx":
/*!*********************************************!*\
  !*** ./react_app/components/LayoutCard.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);


const LayoutCard = ({
  layout,
  viewMode,
  onImport,
  onPreview
}) => {
  const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);

  /**
   * Handle image load success
   */
  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  /**
   * Handle image load error
   */
  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(true);
  };

  /**
   * Handle import button click
   */
  const handleImportClick = e => {
    e.preventDefault();
    e.stopPropagation();
    onImport();
  };

  /**
   * Handle preview button click
   */
  const handlePreviewClick = e => {
    e.preventDefault();
    e.stopPropagation();
    onPreview();
  };

  /**
   * Render card actions
   */
  const renderActions = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__actions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--primary dll-button--small",
    onClick: handleImportClick,
    title: "Import this layout"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-download"
  }), "Import"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "dll-button dll-button--secondary dll-button--small",
    onClick: handlePreviewClick,
    title: "Preview this layout"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-visibility"
  }), "Preview"));

  /**
   * Render preview image
   */
  const renderPreviewImage = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__image-container"
  }, !imageLoaded && !imageError && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__image-placeholder"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-loading__spinner dll-loading__spinner--small"
  })), imageError ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__image-error"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-format-image"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, "Image not available")) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: layout.previewImage,
    alt: layout.name,
    className: `dll-layout-card__image ${isHovered ? 'dll-layout-card__image--scrolling' : ''}`,
    onLoad: handleImageLoad,
    onError: handleImageError,
    style: {
      display: imageLoaded ? 'block' : 'none'
    }
  }), isHovered && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__overlay"
  }, renderActions()));

  /**
   * Render card content
   */
  const renderContent = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", {
    className: "dll-layout-card__title"
  }, layout.name), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-layout-card__category"
  }, layout.category), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "dll-layout-card__description"
  }, layout.description), viewMode === 'list' && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__actions-list"
  }, renderActions()));

  // Grid view layout
  if (viewMode === 'grid') {
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "dll-layout-card dll-layout-card--grid",
      onMouseEnter: () => setIsHovered(true),
      onMouseLeave: () => setIsHovered(false)
    }, renderPreviewImage(), renderContent());
  }

  // List view layout
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card dll-layout-card--list",
    onMouseEnter: () => setIsHovered(true),
    onMouseLeave: () => setIsHovered(false)
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__image-wrapper"
  }, renderPreviewImage()), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-layout-card__content-wrapper"
  }, renderContent()));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LayoutCard);

/***/ }),

/***/ "./react_app/components/Sidebar.jsx":
/*!******************************************!*\
  !*** ./react_app/components/Sidebar.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);


const Sidebar = ({
  categories,
  selectedCategory,
  onCategoryChange
}) => {
  /**
   * Handle category click
   */
  const handleCategoryClick = category => {
    onCategoryChange(category);
  };

  /**
   * Get category display name
   */
  const getCategoryDisplayName = category => {
    if (category === 'all') {
      return 'All Categories';
    }
    return category;
  };

  /**
   * Get category count (placeholder for future implementation)
   */
  const getCategoryCount = category => {
    // This could be enhanced to show actual counts
    return '';
  };
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-sidebar"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-sidebar__header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h3", {
    className: "dll-sidebar__title"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dashicons dashicons-category"
  }), "Categories")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "dll-sidebar__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    className: "dll-category-list"
  }, categories.map(category => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    key: category,
    className: "dll-category-list__item"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: `dll-category-list__button ${selectedCategory === category ? 'dll-category-list__button--active' : ''}`,
    onClick: () => handleCategoryClick(category)
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-category-list__name"
  }, getCategoryDisplayName(category)), getCategoryCount(category) && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "dll-category-list__count"
  }, getCategoryCount(category))))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);

/***/ }),

/***/ "./react_app/services/ApiService.js":
/*!******************************************!*\
  !*** ./react_app/services/ApiService.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * API Service for handling AJAX requests to backend endpoints
 */
class ApiService {
  constructor() {
    this.ajaxUrl = window.dllAjax?.ajaxUrl || '/wp-admin/admin-ajax.php';
    this.nonce = window.dllAjax?.nonce || '';
    this.strings = window.dllAjax?.strings || {};
  }

  /**
   * Make AJAX request to WordPress backend
   * 
   * @param {string} action The WordPress AJAX action
   * @param {Object} data Additional data to send
   * @param {Object} options Request options
   * @returns {Promise} Promise that resolves with response data
   */
  async makeRequest(action, data = {}, options = {}) {
    const requestData = {
      action,
      nonce: this.nonce,
      ...data
    };
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams(requestData),
      ...options
    };
    try {
      const response = await fetch(this.ajaxUrl, requestOptions);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.data?.message || this.strings.error || 'Request failed');
      }
      return result.data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  /**
   * Format builder layout file (similar to Divi's formatBuilderLayoutFile)
   * Converts et_builder context to et_builder_layouts format
   * @param {File} file - The JSON file to format
   * @returns {Promise<File>} - Promise resolving to formatted file
   */
  async formatBuilderLayoutFile(file) {
    const reader = new FileReader();
    return new Promise((resolve, reject) => {
      reader.onloadend = e => {
        let content = '';
        try {
          content = JSON.parse(e.target.result);
        } catch (e) {
          const importFile = new File([JSON.stringify({})], file.name, {
            type: 'application/json'
          });
          return resolve(importFile);
        }
        if ('et_builder' === content.context) {
          const name = file.name.replace('.json', '');
          const postId = Object.keys(content.data)[0];
          const postContent = content.data[postId];
          const convertedFile = {
            ...content,
            context: 'et_builder_layouts',
            data: {
              [postId]: {
                ID: parseInt(postId, 10),
                post_title: name,
                post_name: name,
                post_content: postContent,
                post_excerpt: '',
                post_status: 'publish',
                comment_status: 'closed',
                ping_status: 'closed',
                post_type: 'et_pb_layout',
                post_meta: {
                  _et_pb_built_for_post_type: ['page']
                },
                terms: {
                  1: {
                    name: 'layout',
                    slug: 'layout',
                    taxonomy: 'layout_type'
                  }
                }
              }
            }
          };
          const importFile = new File([JSON.stringify(convertedFile)], file.name, {
            type: 'application/json'
          });
          resolve(importFile);
        } else {
          resolve(file);
        }
      };
      reader.onerror = () => {
        reader.abort();
        reject();
      };
      reader.readAsText(file);
    });
  }

  /**
   * Import layout using Divi's native portability system
   * @param {File} file - The JSON file to import
   * @param {Object} options - Import options
   * @returns {Promise} - Promise resolving to import result
   */
  async importLayout(file, options = {}) {
    try {
      // Debug: Check what's available
      console.log('window.etCore:', window.etCore);
      console.log('window.etCorePortability:', window.etCorePortability);

      // Check if Divi's portability object is available
      if (!window.etCore || !window.etCore.portability) {
        console.warn('Divi portability system not available, falling back to direct AJAX');
        // Fallback to our previous jQuery implementation
        return this.importLayoutFallback(file, options);
      }

      // Format the file using Divi's logic
      const formattedFile = await this.formatBuilderLayoutFile(file);

      // Determine context from the formatted file
      const fileContent = await this.readFileAsText(formattedFile);
      let context = 'et_builder_layouts'; // Default context

      try {
        const parsedContent = JSON.parse(fileContent);
        context = parsedContent.context || 'et_builder_layouts';
      } catch (e) {
        // Use default context if parsing fails
      }
      console.log('Using Divi portability system with context:', context);
      console.log('Available etCore.portability methods:', Object.keys(window.etCore.portability));

      // Use Divi's native portability system
      return new Promise((resolve, reject) => {
        // Prepare data exactly like Divi's ajaxAction method
        const importData = {
          action: 'et_core_portability_import',
          context: context,
          file: formattedFile,
          content: false,
          timestamp: 0,
          post: options.createPage ? 0 : jQuery('#post_ID').val() || 0,
          replace: options.createPage ? '0' : '0',
          include_global_presets: options.includeGlobalPresets ? '1' : '0',
          page: 1,
          nonce: window.etCorePortability?.nonces?.import || window.dllAjax.portability_nonce
        };
        console.log('Import data:', importData);

        // Use Divi's ajaxAction method directly
        window.etCore.portability.ajaxAction(importData, function (response) {
          console.log('Divi portability response:', response);

          // Handle page creation if requested
          if (options.createPage && response && response.data) {
            this.createPageWithLayout(response.data, options.pageTitle).then(resolve).catch(reject);
            return;
          }

          // Success response from Divi
          resolve({
            success: true,
            data: response.data || response,
            message: 'Layout imported successfully'
          });
        }.bind(this), true); // true for file support
      });
    } catch (error) {
      console.error('Import error:', error);
      return {
        success: false,
        message: error.message || 'Import preparation failed'
      };
    }
  }

  /**
   * Fallback import method using direct jQuery AJAX (if Divi's portability system isn't available)
   * @param {File} file - The JSON file to import
   * @param {Object} options - Import options
   * @returns {Promise} - Promise resolving to import result
   */
  async importLayoutFallback(file, options = {}) {
    try {
      // Format the file using Divi's logic
      const formattedFile = await this.formatBuilderLayoutFile(file);

      // Determine context from the formatted file
      const fileContent = await this.readFileAsText(formattedFile);
      let context = 'et_builder_layouts'; // Default context

      try {
        const parsedContent = JSON.parse(fileContent);
        context = parsedContent.context || 'et_builder_layouts';
      } catch (e) {
        // Use default context if parsing fails
      }
      console.log('Using fallback jQuery AJAX with context:', context);

      // Use jQuery AJAX as fallback
      return new Promise((resolve, reject) => {
        const ajaxData = {
          action: 'et_core_portability_import',
          context: context,
          nonce: window.dllAjax.portability_nonce,
          file: formattedFile,
          content: false,
          timestamp: 0,
          post: options.createPage ? 0 : jQuery('#post_ID').val() || 0,
          replace: options.createPage ? '0' : '0',
          include_global_presets: options.includeGlobalPresets ? '1' : '0',
          page: 1
        };
        const formData = new FormData();
        Object.keys(ajaxData).forEach(function (name) {
          const value = ajaxData[name];
          if ('file' === name) {
            formData.append('file', value, value.name);
          } else {
            formData.append(name, value);
          }
        });
        jQuery.ajax({
          type: 'POST',
          url: this.ajaxUrl,
          data: formData,
          processData: false,
          contentType: false,
          success: response => {
            console.log('Fallback import response:', response);
            if (response && ('undefined' !== typeof response.data || response.success !== false)) {
              resolve({
                success: true,
                data: response.data || response,
                message: 'Layout imported successfully (fallback)'
              });
            } else {
              reject(new Error('Import failed - no data returned'));
            }
          },
          error: (xhr, status, error) => {
            console.error('Fallback AJAX error:', xhr, status, error);
            reject(new Error(`Network error: ${error}`));
          }
        });
      });
    } catch (error) {
      console.error('Fallback import error:', error);
      return {
        success: false,
        message: error.message || 'Fallback import preparation failed'
      };
    }
  }

  /**
   * Create page with layout
   * 
   * @param {Object} layoutData The layout data to import
   * @param {string} pageTitle The title for the new page
   * @param {string} pageStatus The page status (draft, publish, etc.)
   * @returns {Promise} Promise that resolves with page creation result
   */
  async createPageWithLayout(layoutData, pageTitle, pageStatus = 'draft') {
    return this.makeRequest('dll_create_page_with_layout', {
      layout_data: JSON.stringify(layoutData),
      page_title: pageTitle,
      page_status: pageStatus
    });
  }

  /**
   * Export layout
   * 
   * @param {number} layoutId The layout ID to export
   * @param {string} exportName Optional name for the export
   * @returns {Promise} Promise that resolves with export result
   */
  async exportLayout(layoutId, exportName = '') {
    return this.makeRequest('dll_export_layout', {
      layout_id: layoutId,
      export_name: exportName
    });
  }

  /**
   * Get available layouts for export
   *
   * @returns {Promise} Promise that resolves with layouts list
   */
  async getAvailableLayouts() {
    return this.makeRequest('dll_get_layouts');
  }

  /**
   * Verify import success by checking if layouts exist in Divi Library
   *
   * @param {Object} importData The data returned from import
   * @returns {Promise} Promise that resolves with verification result
   */
  async verifyImportSuccess(importData) {
    try {
      // If we have layout IDs from the import, we can verify they exist
      if (importData && importData.data && importData.data.timestamp) {
        console.log('Import timestamp:', importData.data.timestamp);

        // Query the Divi Library to see if layouts were created
        // This is a simple verification - in a real implementation,
        // you might want to query specific layout IDs
        const layouts = await this.getAvailableLayouts();
        console.log('Available layouts after import:', layouts);
        return {
          success: true,
          message: 'Import verification completed',
          layoutCount: layouts.length || 0
        };
      }
      return {
        success: false,
        message: 'No import data to verify'
      };
    } catch (error) {
      console.error('Import verification failed:', error);
      return {
        success: false,
        message: 'Verification failed: ' + error.message
      };
    }
  }

  /**
   * Download exported layout as JSON file
   * 
   * @param {Object} exportData The export data from backend
   * @param {string} filename The filename for download
   */
  downloadLayoutFile(exportData, filename) {
    try {
      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      throw new Error('Failed to download layout file');
    }
  }

  /**
   * Load layout data from JSON file
   * 
   * @param {string} jsonUrl URL to the JSON file
   * @returns {Promise} Promise that resolves with layout data
   */
  async loadLayoutFromFile(jsonUrl) {
    try {
      const response = await fetch(jsonUrl);
      if (!response.ok) {
        throw new Error(`Failed to load layout file: ${response.status}`);
      }
      const layoutData = await response.json();

      // Optional: validate layout structure
      if (!this.validateLayoutData(layoutData)) {
        throw new Error('Invalid layout data structure');
      }
      return layoutData;
    } catch (error) {
      console.error('Failed to load layout from file:', error);
      throw error;
    }
  }
  /**
   * Validate layout data structure
   *
   * @param {Object} layoutData The layout data to validate
   * @returns {boolean} True if valid, false otherwise
   */
  validateLayoutData(layoutData) {
    if (!layoutData || typeof layoutData !== 'object') {
      return false;
    }
    // Check for required fields
    const requiredFields = ['context', 'data'];
    for (const field of requiredFields) {
      if (!layoutData.hasOwnProperty(field)) {
        return false;
      }
    }

    // Validate context - accept both et_builder and et_builder_layouts
    const validContexts = ['et_builder', 'et_builder_layouts'];
    if (!validContexts.includes(layoutData.context)) {
      return false;
    }

    // Validate data structure
    if (!layoutData.data || typeof layoutData.data !== 'object') {
      return false;
    }
    return true;
  }

  /**
   * Helper method to read file as text
   * @param {File} file - File to read
   * @returns {Promise<string>} - Promise resolving to file content
   */
  readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target.result);
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  }

  /**
   * Handle file upload for import
   *
   * @param {File} file The file to upload
   * @returns {Promise} Promise that resolves with the file (ready for import)
   */
  async handleFileUpload(file) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('No file provided'));
        return;
      }
      if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
        reject(new Error(this.strings.invalidFile || 'Invalid file format. Please select a JSON file.'));
        return;
      }

      // Basic validation - just check if it's valid JSON
      const reader = new FileReader();
      reader.onload = event => {
        try {
          JSON.parse(event.target.result);
          resolve(file); // Return the original file for import
        } catch (error) {
          reject(new Error('Failed to parse JSON file'));
        }
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsText(file);
    });
  }

  /**
   * Get localized strings
   * 
   * @param {string} key The string key
   * @returns {string} Localized string
   */
  getString(key) {
    return this.strings[key] || key;
  }
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ApiService);

/***/ }),

/***/ "@wordpress/hooks":
/*!*******************************!*\
  !*** external ["wp","hooks"] ***!
  \*******************************/
/***/ ((module) => {

module.exports = window["wp"]["hooks"];

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/***/ ((module) => {

module.exports = window["React"];

/***/ }),

/***/ "react-dom":
/*!***************************!*\
  !*** external "ReactDOM" ***!
  \***************************/
/***/ ((module) => {

module.exports = window["ReactDOM"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!****************************!*\
  !*** ./react_app/index.js ***!
  \****************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ "react-dom");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _App_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./App.jsx */ "./react_app/App.jsx");
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/hooks */ "@wordpress/hooks");
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__);




window.divi_layout_library_hooks = (0,_wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__.createHooks)();
document.addEventListener("DOMContentLoaded", function () {
  const body = document.getElementById("divi-layout-library-body");
  const root = (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createRoot)(body);
  root.render((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_App_jsx__WEBPACK_IMPORTED_MODULE_2__["default"], null));
});
})();

/******/ })()
;
//# sourceMappingURL=divi-layout-library.core.min.js.map