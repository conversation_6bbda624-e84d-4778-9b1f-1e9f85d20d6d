<?php
/*
 * Plugin Name:		Divi Layout Library
 * Plugin URI:		
 * Description:		A great plugin!
 * Version:			1.00
 * Author:			GutenSuite
 * Author URI:		
 * License:			GPL-3.0+
 * License URI:		http://www.gnu.org/licenses/gpl-3.0.txt
 * Author URI:		
 * Text Domain:		divi-layout-library
 * Domain Path:		/languages
 */

if (!defined('ABSPATH')) {
    exit();
}

if (file_exists(dirname(__FILE__) . '/vendor/autoload.php')) {
    require_once dirname(__FILE__) . '/vendor/autoload.php';
}

if (!class_exists('DiviLayoutLibrary')) {
    final class DiviLayoutLibrary
    {
        private function __construct()
        {
            $this->define_constants();
            register_activation_hook(__FILE__, [$this, 'activate']);
            register_deactivation_hook(__FILE__, [$this, 'deactivate']);
            add_action('init', [$this, 'on_plugins_loaded']);
            add_action('divi_layout-library_loaded', [$this, 'init_plugin']);
        }


        public static function init()
        {
            static $instance = false;

            if (!$instance) {
                $instance = new self();
            }

            return $instance;
        }
        public function define_constants()
        {
            /**
             * Defines CONSTANTS for Whole plugins.
             */
            define('DIVI_LAYOUT_LIBARY_SLUG', 'divi-layout-library');
            define('DIVI_LAYOUT_LIBARY_PLUGIN_ROOT_URI', plugins_url('/', __FILE__));
            define('DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH', plugin_dir_path(__FILE__));
            define('DIVI_LAYOUT_LIBARY_ROOT_DIR_URL', plugin_dir_url(__FILE__));
            define('DIVI_LAYOUT_LIBARY_ASSETS_DIR_PATH', DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH . 'assets/');
            define('DIVI_LAYOUT_LIBARY_ASSETS_URI', DIVI_LAYOUT_LIBARY_PLUGIN_ROOT_URI . 'assets/');
        }


        public function on_plugins_loaded()
        {
            do_action('divi_layout-library_loaded');
        }

        /**
         * Initialize the plugin
         *
         * @return void
         */
        public function init_plugin()
        {
            if (is_admin()) {
                new DiviLayoutLibrary\Admin();
            }
        }


        public function load_textdomain()
        {
            // load_plugin_textdomain('betterlinks', false, dirname(plugin_basename(__FILE__)) . '/languages/');
        }


        public function activate(){}
        public function deactivate()
        {
            // new BetterLinks\Uninstall();
        }
    }
}

/**
 * Initializes the main plugin
 *
 * @return \DiviLayoutLibrary
 */
if (!function_exists('DiviLayoutLibrary_Start')) {
    function DiviLayoutLibrary_Start()
    {
        return DiviLayoutLibrary::init();

    }
}

// Plugin Start
DiviLayoutLibrary_Start();