import React, { useState } from 'react';
import ApiService from '../services/ApiService';

const ImportModal = ({ layout, onClose }) => {
    const [importType, setImportType] = useState('library'); // 'library' or 'page'
    const [pageName, setPageName] = useState('');
    const [pageStatus, setPageStatus] = useState('draft');
    const [isImporting, setIsImporting] = useState(false);
    const [progress, setProgress] = useState(0);
    const [importResult, setImportResult] = useState(null);
    const [error, setError] = useState(null);
    const [showConfetti, setShow<PERSON>onfetti] = useState(false);

    const apiService = new ApiService();

    /**
     * Handle import type change
     */
    const handleImportTypeChange = (type) => {
        setImportType(type);
        setError(null);
        
        // Set default page name when switching to page creation
        if (type === 'page' && !pageName) {
            setPageName(layout?.name || '');
        }
    };

    /**
     * Validate form inputs
     */
    const validateInputs = () => {
        if (importType === 'page') {
            if (!pageName.trim()) {
                setError(apiService.getString('pageNameRequired') || 'Page name is required');
                return false;
            }
        }
        return true;
    };

    /**
     * Simulate progress for better UX
     */
    const simulateProgress = () => {
        setProgress(0);
        const interval = setInterval(() => {
            setProgress(prev => {
                if (prev >= 90) {
                    clearInterval(interval);
                    return 90;
                }
                return prev + Math.random() * 20;
            });
        }, 200);
        return interval;
    };

    /**
     * Handle import process
     */
    const handleImport = async () => {
        if (!validateInputs()) {
            return;
        }

        setIsImporting(true);
        setError(null);
        setImportResult(null);
        
        const progressInterval = simulateProgress();


        try {
            // Load layout file from URL and convert to File object
            const response = await fetch(layout.jsonFile);
            if (!response.ok) {
                throw new Error('Failed to load layout file');
            }

            const jsonContent = await response.text();
            const fileName = layout.jsonFile.split('/').pop() || 'layout.json';
            const file = new File([jsonContent], fileName, { type: 'application/json' });

            // Import using Divi's native system
            const importOptions = {
                includeGlobalPresets: false,
                createPage: importType === 'page',
                pageTitle: importType === 'page' ? pageName.trim() : undefined
            };

            console.info(importOptions);

            const result = await apiService.importLayout(file, importOptions);

            // Complete progress
            clearInterval(progressInterval);
            setProgress(100);

            // Verify the import was successful
            if (result.success) {
                console.log('Import completed, verifying...');
                const verification = await apiService.verifyImportSuccess(result);
                console.log('Verification result:', verification);

                // Update result with verification info
                result.verification = verification;
            }

            // Show success state
            setImportResult(result);
            setShowConfetti(true);

            // Auto-hide confetti after 3 seconds
            setTimeout(() => {
                setShowConfetti(false);
            }, 3000);

        } catch (err) {
            clearInterval(progressInterval);
            setError(err.message || apiService.getString('error'));
            setProgress(0);
        } finally {
            setIsImporting(false);
        }
    };

    /**
     * Handle modal close
     */
    const handleClose = () => {
        if (!isImporting) {
            onClose();
        }
    };

    /**
     * Render progress bar
     */
    const renderProgressBar = () => (
        <div className="dll-progress">
            <div className="dll-progress__bar">
                <div 
                    className="dll-progress__fill"
                    style={{ width: `${progress}%` }}
                ></div>
            </div>
            <div className="dll-progress__text">
                {progress < 100 ? `${Math.round(progress)}%` : 'Complete!'}
            </div>
        </div>
    );

    /**
     * Render success state
     */
    const renderSuccess = () => (
        <div className="dll-import-success">
            {showConfetti && (
                <div className="dll-confetti">
                    {/* Simple confetti animation */}
                    {Array.from({ length: 50 }).map((_, i) => (
                        <div
                            key={i}
                            className="dll-confetti__piece"
                            style={{
                                left: `${Math.random() * 100}%`,
                                animationDelay: `${Math.random() * 3}s`,
                                backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)]
                            }}
                        ></div>
                    ))}
                </div>
            )}
            
            <div className="dll-import-success__content">
                <div className="dll-import-success__icon">
                    <span className="dashicons dashicons-yes-alt"></span>
                </div>
                <h3 className="dll-import-success__title">
                    Congratulations! 🎉
                </h3>
                <p className="dll-import-success__message">
                    {importType === 'page' 
                        ? `Page "${pageName}" has been created successfully!`
                        : 'Layout has been imported to your library successfully!'
                    }
                </p>
                
                {importResult?.data?.edit_url && (
                    <div className="dll-import-success__actions">
                        <a
                            href={importResult.data.edit_url}
                            className="dll-button dll-button--primary"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            Edit Page
                        </a>
                        {importResult.data.view_url && (
                            <a
                                href={importResult.data.view_url}
                                className="dll-button dll-button--secondary"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                View Page
                            </a>
                        )}
                    </div>
                )}
            </div>
        </div>
    );

    /**
     * Render error state
     */
    const renderError = () => (
        <div className="dll-import-error">
            <div className="dll-import-error__icon">
                <span>😞</span>
            </div>
            <h3 className="dll-import-error__title">Import Failed</h3>
            <p className="dll-import-error__message">{error}</p>
            <button
                className="dll-button dll-button--primary"
                onClick={() => {
                    setError(null);
                    setProgress(0);
                }}
            >
                Try Again
            </button>
        </div>
    );

    return (
        <div className="dll-modal-overlay" onClick={handleClose}>
            <div className="dll-modal dll-import-modal" onClick={e => e.stopPropagation()}>
                <div className="dll-modal__header">
                    <h2 className="dll-modal__title">
                        Import Layout: {layout?.name}
                    </h2>
                    <button
                        className="dll-modal__close"
                        onClick={handleClose}
                        disabled={isImporting}
                    >
                        <span className="dashicons dashicons-no-alt"></span>
                    </button>
                </div>

                <div className="dll-modal__content">
                    {error ? (
                        renderError()
                    ) : importResult ? (
                        renderSuccess()
                    ) : (
                        <>
                            {isImporting ? (
                                <div className="dll-import-progress">
                                    <p className="dll-import-progress__text">
                                        {apiService.getString('importing')}
                                    </p>
                                    {renderProgressBar()}
                                </div>
                            ) : (
                                <div className="dll-import-form">
                                    <div className="dll-import-options">
                                        <h3>Import Options</h3>
                                        
                                        <label className="dll-radio-option">
                                            <input
                                                type="radio"
                                                name="importType"
                                                value="page"
                                                checked={importType === 'page'}
                                                onChange={() => handleImportTypeChange('page')}
                                            />
                                            <span className="dll-radio-option__label">
                                                <strong>Make a New Page</strong> with this layout
                                            </span>
                                        </label>

                                        <label className="dll-radio-option">
                                            <input
                                                type="radio"
                                                name="importType"
                                                value="library"
                                                checked={importType === 'library'}
                                                onChange={() => handleImportTypeChange('library')}
                                            />
                                            <span className="dll-radio-option__label">
                                                <strong>Just Import Layout</strong> (add to Divi library)
                                            </span>
                                        </label>
                                    </div>

                                    {importType === 'page' && (
                                        <div className="dll-page-options">
                                            <div className="dll-form-group">
                                                <label htmlFor="pageName" className="dll-form-label">
                                                    Page Name *
                                                </label>
                                                <input
                                                    type="text"
                                                    id="pageName"
                                                    className="dll-form-input"
                                                    value={pageName}
                                                    onChange={(e) => setPageName(e.target.value)}
                                                    placeholder="Enter page name"
                                                    required
                                                />
                                            </div>

                                            <div className="dll-form-group">
                                                <label htmlFor="pageStatus" className="dll-form-label">
                                                    Page Status
                                                </label>
                                                <select
                                                    id="pageStatus"
                                                    className="dll-form-select"
                                                    value={pageStatus}
                                                    onChange={(e) => setPageStatus(e.target.value)}
                                                >
                                                    <option value="draft">Draft</option>
                                                    <option value="publish">Published</option>
                                                    <option value="private">Private</option>
                                                </select>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </>
                    )}
                </div>

                {!isImporting && !importResult && !error && (
                    <div className="dll-modal__footer">
                        <button
                            className="dll-button dll-button--secondary"
                            onClick={handleClose}
                        >
                            Cancel
                        </button>
                        <button
                            className="dll-button dll-button--primary"
                            onClick={handleImport}
                        >
                            <span className="dashicons dashicons-download"></span>
                            Import Layout
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ImportModal;
