import React from 'react';

const Sidebar = ({ categories, selectedCategory, onCategoryChange }) => {
    /**
     * Handle category click
     */
    const handleCategoryClick = (category) => {
        onCategoryChange(category);
    };

    /**
     * Get category display name
     */
    const getCategoryDisplayName = (category) => {
        if (category === 'all') {
            return 'All Categories';
        }
        return category;
    };

    /**
     * Get category count (placeholder for future implementation)
     */
    const getCategoryCount = (category) => {
        // This could be enhanced to show actual counts
        return '';
    };

    return (
        <div className="dll-sidebar">
            <div className="dll-sidebar__header">
                <h3 className="dll-sidebar__title">
                    <span className="dashicons dashicons-category"></span>
                    Categories
                </h3>
            </div>
            
            <div className="dll-sidebar__content">
                <ul className="dll-category-list">
                    {categories.map(category => (
                        <li key={category} className="dll-category-list__item">
                            <button
                                className={`dll-category-list__button ${
                                    selectedCategory === category 
                                        ? 'dll-category-list__button--active' 
                                        : ''
                                }`}
                                onClick={() => handleCategoryClick(category)}
                            >
                                <span className="dll-category-list__name">
                                    {getCategoryDisplayName(category)}
                                </span>
                                {getCategoryCount(category) && (
                                    <span className="dll-category-list__count">
                                        {getCategoryCount(category)}
                                    </span>
                                )}
                            </button>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default Sidebar;
