import React, { useState } from 'react';

const LayoutCard = ({ layout, viewMode, onImport, onPreview }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    /**
     * Handle image load success
     */
    const handleImageLoad = () => {
        setImageLoaded(true);
    };

    /**
     * Handle image load error
     */
    const handleImageError = () => {
        setImageError(true);
        setImageLoaded(true);
    };

    /**
     * Handle import button click
     */
    const handleImportClick = (e) => {
        e.preventDefault();
        e.stopPropagation();
        onImport();
    };

    /**
     * Handle preview button click
     */
    const handlePreviewClick = (e) => {
        e.preventDefault();
        e.stopPropagation();
        onPreview();
    };

    /**
     * Render card actions
     */
    const renderActions = () => (
        <div className="dll-layout-card__actions">
            <button
                className="dll-button dll-button--primary dll-button--small"
                onClick={handleImportClick}
                title="Import this layout"
            >
                <span className="dashicons dashicons-download"></span>
                Import
            </button>
            <button
                className="dll-button dll-button--secondary dll-button--small"
                onClick={handlePreviewClick}
                title="Preview this layout"
            >
                <span className="dashicons dashicons-visibility"></span>
                Preview
            </button>
        </div>
    );

    /**
     * Render preview image
     */
    const renderPreviewImage = () => (
        <div className="dll-layout-card__image-container">
            {!imageLoaded && !imageError && (
                <div className="dll-layout-card__image-placeholder">
                    <div className="dll-loading__spinner dll-loading__spinner--small"></div>
                </div>
            )}
            
            {imageError ? (
                <div className="dll-layout-card__image-error">
                    <span className="dashicons dashicons-format-image"></span>
                    <span>Image not available</span>
                </div>
            ) : (
                <img
                    src={layout.previewImage}
                    alt={layout.name}
                    className={`dll-layout-card__image ${isHovered ? 'dll-layout-card__image--scrolling' : ''}`}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    style={{ display: imageLoaded ? 'block' : 'none' }}
                />
            )}
            
            {isHovered && (
                <div className="dll-layout-card__overlay">
                    {renderActions()}
                </div>
            )}
        </div>
    );

    /**
     * Render card content
     */
    const renderContent = () => (
        <div className="dll-layout-card__content">
            <h3 className="dll-layout-card__title">{layout.name}</h3>
            <p className="dll-layout-card__category">{layout.category}</p>
            <p className="dll-layout-card__description">{layout.description}</p>
            
            {viewMode === 'list' && (
                <div className="dll-layout-card__actions-list">
                    {renderActions()}
                </div>
            )}
        </div>
    );

    // Grid view layout
    if (viewMode === 'grid') {
        return (
            <div
                className="dll-layout-card dll-layout-card--grid"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                {renderPreviewImage()}
                {renderContent()}
            </div>
        );
    }

    // List view layout
    return (
        <div
            className="dll-layout-card dll-layout-card--list"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div className="dll-layout-card__image-wrapper">
                {renderPreviewImage()}
            </div>
            <div className="dll-layout-card__content-wrapper">
                {renderContent()}
            </div>
        </div>
    );
};

export default LayoutCard;
