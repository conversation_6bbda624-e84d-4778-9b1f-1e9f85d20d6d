import React, { useState, useEffect } from 'react';
import ApiService from '../services/ApiService';
import LayoutCard from './LayoutCard';
import Sidebar from './Sidebar';
import ImportModal from './ImportModal';
import ExportModal from './ExportModal';

const { plugin_root_url } = window.dllAjax;

const Dashboard = () => {
    const [layouts, setLayouts] = useState([]);
    const [filteredLayouts, setFilteredLayouts] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showImportModal, setShowImportModal] = useState(false);
    const [showExportModal, setShowExportModal] = useState(false);
    const [selectedLayout, setSelectedLayout] = useState(null);

    const apiService = new ApiService();

    useEffect(() => {
        loadPredefinedLayouts();
    }, []);

    useEffect(() => {
        filterLayouts();
    }, [layouts, selectedCategory]);

    /**
     * Load predefined layouts from static data
     */
    const loadPredefinedLayouts = () => {
        try {
            setLoading(true);

            // Sample predefined layouts data
            const predefinedLayouts = [
                {
                    id: 'layout-1',
                    name: 'Modern Business',
                    category: 'Business',
                    description: 'A clean and modern business layout for corporate sites.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/business-modern.jpg',
                    previewLink: 'https://demo.example.com/business-modern',
                    jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Web-Services.json'
                },
                {
                    id: 'layout-2',
                    name: 'Creative Portfolio',
                    category: 'Portfolio',
                    description: 'A creative portfolio layout perfect for showcasing work.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/portfolio-creative.jpg',
                    previewLink: 'https://demo.example.com/portfolio-creative',
                    jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/portfolio-creative.json'
                },
                {
                    id: 'layout-3',
                    name: 'Restaurant Menu',
                    category: 'Restaurant',
                    description: 'An elegant restaurant layout with menu showcase.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/restaurant-menu.jpg',
                    previewLink: 'https://demo.example.com/restaurant-menu',
                    jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/restaurant-menu.json'
                },
                {
                    id: 'layout-4',
                    name: 'Tech Startup',
                    category: 'Business',
                    description: 'A modern tech startup layout with bold design.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/tech-startup.jpg',
                    previewLink: 'https://demo.example.com/tech-startup',
                    jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/blurbcore.json'
                },
                {
                    id: 'layout-5',
                    name: 'Photography Studio',
                    category: 'Portfolio',
                    description: 'A stunning photography portfolio layout.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/photography-studio.jpg',
                    previewLink: 'https://demo.example.com/photography-studio',
                    jsonFile: '/wp-content/plugins/divi-layout-library/assets/layouts/photography-studio.json'
                },
                {
                    id: 'layout-6',
                    name: 'Coffee Shop',
                    category: 'Restaurant',
                    description: 'A cozy coffee shop layout with warm colors.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg',
                    previewLink: 'https://demo.example.com/coffee-shop',
                    jsonFile: '/wp-content/plugins/divi-layout-library/assets/layouts/coffee-shop.json'
                },
                {
                    id: 'layout-7',
                    name: 'Gardener Shop',
                    category: 'Garden',
                    description: 'A garden shop.',
                    previewImage: '/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg',
                    previewLink: 'https://demo.example.com/coffee-shop',
                    jsonFile: 'https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Gardener-All-Layouts-Import.json'
                }
            ];

            setLayouts(predefinedLayouts);
            setLoading(false);
        } catch (err) {
            setError('Failed to load layouts');
            setLoading(false);
        }
    };

    /**
     * Filter layouts by selected category
     */
    const filterLayouts = () => {
        if (selectedCategory === 'all') {
            setFilteredLayouts(layouts);
        } else {
            setFilteredLayouts(layouts.filter(layout => layout.category === selectedCategory));
        }
    };

    /**
     * Get unique categories from layouts
     */
    const getCategories = () => {
        const categories = ['all'];
        layouts.forEach(layout => {
            if (!categories.includes(layout.category)) {
                categories.push(layout.category);
            }
        });
        return categories;
    };

    /**
     * Handle layout import
     */
    const handleImportLayout = (layout) => {
        setSelectedLayout(layout);
        setShowImportModal(true);
    };

    /**
     * Handle layout preview
     */
    const handlePreviewLayout = (layout) => {
        if (layout.previewLink) {
            window.open(layout.previewLink, '_blank');
        }
    };

    /**
     * Handle export button click
     */
    const handleExportClick = () => {
        setShowExportModal(true);
    };

    /**
     * Toggle view mode between grid and list
     */
    const toggleViewMode = () => {
        setViewMode(viewMode === 'grid' ? 'list' : 'grid');
    };

    if (loading) {
        return (
            <div className="dll-dashboard dll-dashboard--loading">
                <div className="dll-loading">
                    <div className="dll-loading__spinner"></div>
                    <p>Loading layouts...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="dll-dashboard dll-dashboard--error">
                <div className="dll-error">
                    <h3>Error</h3>
                    <p>{error}</p>
                    <button onClick={loadPredefinedLayouts} className="dll-button dll-button--primary">
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="dll-dashboard">
            <div className="dll-dashboard__header">
                <h1 className="dll-dashboard__title">Divi Layout Library</h1>
                <div className="dll-dashboard__toolbar">
                    <div className="dll-view-toggle">
                        <button
                            className={`dll-view-toggle__button ${viewMode === 'grid' ? 'dll-view-toggle__button--active' : ''}`}
                            onClick={() => setViewMode('grid')}
                            title="Grid View"
                        >
                            <span className="dashicons dashicons-grid-view"></span>
                        </button>
                        <button
                            className={`dll-view-toggle__button ${viewMode === 'list' ? 'dll-view-toggle__button--active' : ''}`}
                            onClick={() => setViewMode('list')}
                            title="List View"
                        >
                            <span className="dashicons dashicons-list-view"></span>
                        </button>
                    </div>
                    <button
                        className="dll-button dll-button--secondary"
                        onClick={handleExportClick}
                    >
                        <span className="dashicons dashicons-download"></span>
                        Export Layout
                    </button>
                </div>
            </div>

            <div className="dll-dashboard__content">
                <Sidebar
                    categories={getCategories()}
                    selectedCategory={selectedCategory}
                    onCategoryChange={setSelectedCategory}
                />

                <div className="dll-dashboard__main">
                    <div className={`dll-layouts dll-layouts--${viewMode}`}>
                        {filteredLayouts.length === 0 ? (
                            <div className="dll-layouts__empty">
                                <p>No layouts found for the selected category.</p>
                            </div>
                        ) : (
                            filteredLayouts.map(layout => (
                                <LayoutCard
                                    key={layout.id}
                                    layout={layout}
                                    viewMode={viewMode}
                                    onImport={() => handleImportLayout(layout)}
                                    onPreview={() => handlePreviewLayout(layout)}
                                />
                            ))
                        )}
                    </div>
                </div>
            </div>

            {showImportModal && (
                <ImportModal
                    layout={selectedLayout}
                    onClose={() => {
                        setShowImportModal(false);
                        setSelectedLayout(null);
                    }}
                />
            )}

            {showExportModal && (
                <ExportModal
                    onClose={() => setShowExportModal(false)}
                />
            )}
        </div>
    );
};

export default Dashboard;
