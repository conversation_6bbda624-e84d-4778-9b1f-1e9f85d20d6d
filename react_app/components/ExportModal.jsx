import React, { useState, useEffect } from 'react';
import ApiService from '../services/ApiService';

const ExportModal = ({ onClose }) => {
    const [layouts, setLayouts] = useState([]);
    const [selectedLayout, setSelectedLayout] = useState(null);
    const [exportName, setExportName] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [isExporting, setIsExporting] = useState(false);
    const [error, setError] = useState(null);
    const [exportSuccess, setExportSuccess] = useState(false);

    const apiService = new ApiService();

    useEffect(() => {
        loadAvailableLayouts();
    }, []);

    /**
     * Load available layouts for export
     */
    const loadAvailableLayouts = async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            const result = await apiService.getAvailableLayouts();
            setLayouts(result.layouts || []);
            
        } catch (err) {
            setError(err.message || 'Failed to load layouts');
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handle layout selection
     */
    const handleLayoutSelect = (layout) => {
        setSelectedLayout(layout);
        setExportName(layout.title || '');
        setError(null);
    };

    /**
     * Handle export process
     */
    const handleExport = async () => {
        if (!selectedLayout) {
            setError(apiService.getString('selectLayout') || 'Please select a layout to export');
            return;
        }

        setIsExporting(true);
        setError(null);

        try {
            // Export the layout
            const result = await apiService.exportLayout(
                selectedLayout.id,
                exportName.trim() || selectedLayout.title
            );

            // Download the exported file
            const filename = exportName.trim() || selectedLayout.title || 'divi_layout';
            apiService.downloadLayoutFile(result.export_data, filename);

            // Show success state
            setExportSuccess(true);

            // Auto-close after 2 seconds
            setTimeout(() => {
                onClose();
            }, 2000);

        } catch (err) {
            setError(err.message || 'Export failed');
        } finally {
            setIsExporting(false);
        }
    };

    /**
     * Handle modal close
     */
    const handleClose = () => {
        if (!isExporting) {
            onClose();
        }
    };

    /**
     * Format date for display
     */
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    /**
     * Render loading state
     */
    const renderLoading = () => (
        <div className="dll-export-loading">
            <div className="dll-loading__spinner"></div>
            <p>Loading available layouts...</p>
        </div>
    );

    /**
     * Render error state
     */
    const renderError = () => (
        <div className="dll-export-error">
            <div className="dll-export-error__icon">
                <span className="dashicons dashicons-warning"></span>
            </div>
            <h3>Error</h3>
            <p>{error}</p>
            <button
                className="dll-button dll-button--primary"
                onClick={loadAvailableLayouts}
            >
                Try Again
            </button>
        </div>
    );

    /**
     * Render success state
     */
    const renderSuccess = () => (
        <div className="dll-export-success">
            <div className="dll-export-success__icon">
                <span className="dashicons dashicons-yes-alt"></span>
            </div>
            <h3>Export Successful!</h3>
            <p>Your layout has been downloaded successfully.</p>
        </div>
    );

    /**
     * Render layout list
     */
    const renderLayoutList = () => (
        <div className="dll-layout-list">
            {layouts.length === 0 ? (
                <div className="dll-layout-list__empty">
                    <p>No layouts available for export.</p>
                    <p>Create some pages with Divi Builder first.</p>
                </div>
            ) : (
                <div className="dll-layout-list__items">
                    {layouts.map(layout => (
                        <div
                            key={layout.id}
                            className={`dll-layout-item ${
                                selectedLayout?.id === layout.id 
                                    ? 'dll-layout-item--selected' 
                                    : ''
                            }`}
                            onClick={() => handleLayoutSelect(layout)}
                        >
                            <div className="dll-layout-item__content">
                                <h4 className="dll-layout-item__title">
                                    {layout.title || 'Untitled'}
                                </h4>
                                <div className="dll-layout-item__meta">
                                    <span className="dll-layout-item__type">
                                        {layout.type}
                                    </span>
                                    <span className="dll-layout-item__status">
                                        {layout.status}
                                    </span>
                                    <span className="dll-layout-item__date">
                                        Modified: {formatDate(layout.modified)}
                                    </span>
                                </div>
                            </div>
                            <div className="dll-layout-item__actions">
                                <a
                                    href={layout.edit_url}
                                    className="dll-layout-item__edit"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    onClick={e => e.stopPropagation()}
                                    title="Edit this layout"
                                >
                                    <span className="dashicons dashicons-edit"></span>
                                </a>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );

    /**
     * Render export form
     */
    const renderExportForm = () => (
        <div className="dll-export-form">
            <div className="dll-form-group">
                <label htmlFor="exportName" className="dll-form-label">
                    Export Name (optional)
                </label>
                <input
                    type="text"
                    id="exportName"
                    className="dll-form-input"
                    value={exportName}
                    onChange={(e) => setExportName(e.target.value)}
                    placeholder="Enter custom name for export"
                />
                <p className="dll-form-help">
                    Leave empty to use the layout title as filename.
                </p>
            </div>
        </div>
    );

    return (
        <div className="dll-modal-overlay" onClick={handleClose}>
            <div className="dll-modal dll-export-modal" onClick={e => e.stopPropagation()}>
                <div className="dll-modal__header">
                    <h2 className="dll-modal__title">
                        <span className="dashicons dashicons-download"></span>
                        Export Layout
                    </h2>
                    <button
                        className="dll-modal__close"
                        onClick={handleClose}
                        disabled={isExporting}
                    >
                        <span className="dashicons dashicons-no-alt"></span>
                    </button>
                </div>

                <div className="dll-modal__content">
                    {isLoading ? (
                        renderLoading()
                    ) : error && !selectedLayout ? (
                        renderError()
                    ) : exportSuccess ? (
                        renderSuccess()
                    ) : (
                        <>
                            <div className="dll-export-step">
                                <h3>1. Select Layout to Export</h3>
                                {renderLayoutList()}
                            </div>

                            {selectedLayout && (
                                <div className="dll-export-step">
                                    <h3>2. Export Options</h3>
                                    {renderExportForm()}
                                </div>
                            )}

                            {error && (
                                <div className="dll-export-error-inline">
                                    <span className="dashicons dashicons-warning"></span>
                                    {error}
                                </div>
                            )}
                        </>
                    )}
                </div>

                {!isLoading && !exportSuccess && layouts.length > 0 && (
                    <div className="dll-modal__footer">
                        <button
                            className="dll-button dll-button--secondary"
                            onClick={handleClose}
                            disabled={isExporting}
                        >
                            Cancel
                        </button>
                        <button
                            className="dll-button dll-button--primary"
                            onClick={handleExport}
                            disabled={!selectedLayout || isExporting}
                        >
                            {isExporting ? (
                                <>
                                    <div className="dll-loading__spinner dll-loading__spinner--small"></div>
                                    {apiService.getString('exporting')}
                                </>
                            ) : (
                                <>
                                    <span className="dashicons dashicons-download"></span>
                                    Export & Download
                                </>
                            )}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ExportModal;
