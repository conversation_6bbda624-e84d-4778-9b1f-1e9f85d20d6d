<?php

namespace DiviLayoutLibrary\Core;

/**
 * Dependency Injection Container
 *
 * Simple container for managing dependencies and services
 */
class Container {

    /**
     * @var array Registered services
     */
    private $services = [];

    /**
     * @var array Service instances
     */
    private $instances = [];

    /**
     * @var Container Singleton instance
     */
    private static $instance = null;

    /**
     * Get container instance
     *
     * @return Container
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {}

    /**
     * Register a service
     *
     * @param string $name Service name
     * @param callable $factory Factory function
     * @param bool $singleton Whether to create as singleton
     */
    public function register($name, callable $factory, $singleton = true) {
        $this->services[$name] = [
            'factory' => $factory,
            'singleton' => $singleton
        ];
    }

    /**
     * Get a service instance
     *
     * @param string $name Service name
     * @return mixed Service instance
     * @throws \Exception If service not found
     */
    public function get($name) {
        if (!isset($this->services[$name])) {
            throw new \Exception("Service '{$name}' not found");
        }

        $service = $this->services[$name];

        // Return existing instance if singleton
        if ($service['singleton'] && isset($this->instances[$name])) {
            return $this->instances[$name];
        }

        // Create new instance
        $instance = $service['factory']($this);

        // Store instance if singleton
        if ($service['singleton']) {
            $this->instances[$name] = $instance;
        }

        return $instance;
    }

    /**
     * Check if service is registered
     *
     * @param string $name Service name
     * @return bool
     */
    public function has($name) {
        return isset($this->services[$name]);
    }

    /**
     * Set a service instance directly
     *
     * @param string $name Service name
     * @param mixed $instance Service instance
     */
    public function set($name, $instance) {
        $this->instances[$name] = $instance;
    }

    /**
     * Remove a service
     *
     * @param string $name Service name
     */
    public function remove($name) {
        unset($this->services[$name], $this->instances[$name]);
    }

    /**
     * Get all registered service names
     *
     * @return array
     */
    public function getServiceNames() {
        return array_keys($this->services);
    }
}