<?php

namespace DiviLayoutLibrary\Core;

use DiviLayoutLibrary\Admin\AdminController;
use DiviLayoutLibrary\Services\LayoutService;
use DiviLayoutLibrary\Services\ImportService;
use DiviLayoutLibrary\Services\ExportService;
use DiviLayoutLibrary\Services\CategoryService;
use DiviLayoutLibrary\Services\MediaService;
use DiviLayoutLibrary\Services\CacheService;
use DiviLayoutLibrary\Repositories\LayoutRepository;
use DiviLayoutLibrary\Repositories\CategoryRepository;
use DiviLayoutLibrary\Utilities\Validator;
use DiviLayoutLibrary\Utilities\Sanitizer;
use DiviLayoutLibrary\Utilities\Logger;

/**
 * Main Plugin Class
 *
 * Handles plugin initialization, dependency injection, and lifecycle management
 */
class Plugin {

    /**
     * @var Plugin Singleton instance
     */
    private static $instance = null;

    /**
     * @var Container Dependency injection container
     */
    private $container;

    /**
     * @var string Plugin version
     */
    private $version;

    /**
     * @var string Plugin path
     */
    private $plugin_path;

    /**
     * @var string Plugin URL
     */
    private $plugin_url;

    /**
     * Get plugin instance
     *
     * @return Plugin
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Private constructor
     */
    private function __construct() {
        $this->version = '2.0.0';
        $this->plugin_path = DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH;
        $this->plugin_url = DIVI_LAYOUT_LIBARY_ROOT_DIR_URL;

        $this->container = Container::getInstance();
        $this->registerServices();
        $this->initHooks();
    }

    /**
     * Register all services in the container
     */
    private function registerServices() {
        // Utilities
        $this->container->register('validator', function($container) {
            return new Validator();
        });

        $this->container->register('sanitizer', function($container) {
            return new Sanitizer();
        });

        $this->container->register('logger', function($container) {
            return new Logger();
        });

        // Repositories
        $this->container->register('layout_repository', function($container) {
            return new LayoutRepository();
        });

        $this->container->register('category_repository', function($container) {
            return new CategoryRepository();
        });

        // Services
        $this->container->register('cache_service', function($container) {
            return new CacheService();
        });

        $this->container->register('media_service', function($container) {
            return new MediaService(
                $container->get('logger')
            );
        });

        $this->container->register('category_service', function($container) {
            return new CategoryService(
                $container->get('category_repository'),
                $container->get('cache_service')
            );
        });

        $this->container->register('import_service', function($container) {
            return new ImportService(
                $container->get('media_service'),
                $container->get('validator'),
                $container->get('sanitizer'),
                $container->get('logger')
            );
        });

        $this->container->register('export_service', function($container) {
            return new ExportService(
                $container->get('media_service'),
                $container->get('logger')
            );
        });

        $this->container->register('layout_service', function($container) {
            return new LayoutService(
                $container->get('layout_repository'),
                $container->get('import_service'),
                $container->get('export_service'),
                $container->get('cache_service'),
                $container->get('validator')
            );
        });

        // Admin Controller
        $this->container->register('admin_controller', function($container) {
            return new AdminController($container);
        });
    }

    /**
     * Initialize WordPress hooks
     */
    private function initHooks() {
        add_action('init', [$this, 'onInit']);
        add_action('admin_init', [$this, 'onAdminInit']);

        register_activation_hook($this->getPluginFile(), [$this, 'onActivation']);
        register_deactivation_hook($this->getPluginFile(), [$this, 'onDeactivation']);
    }

    /**
     * Handle plugin initialization
     */
    public function onInit() {
        // Load text domain
        load_plugin_textdomain(
            'divi-layout-library',
            false,
            dirname(plugin_basename($this->getPluginFile())) . '/languages'
        );

        // Initialize admin if in admin area
        if (is_admin()) {
            $this->container->get('admin_controller');
        }

        do_action('dll_plugin_loaded', $this);
    }

    /**
     * Handle admin initialization
     */
    public function onAdminInit() {
        // Check if Divi is available
        if (!$this->isDiviAvailable()) {
            add_action('admin_notices', [$this, 'showDiviRequiredNotice']);
            return;
        }

        do_action('dll_admin_loaded', $this);
    }

    /**
     * Handle plugin activation
     */
    public function onActivation() {
        // Create database tables if needed
        $this->createDatabaseTables();

        // Set default options
        $this->setDefaultOptions();

        // Flush rewrite rules
        flush_rewrite_rules();

        do_action('dll_plugin_activated', $this);
    }

    /**
     * Handle plugin deactivation
     */
    public function onDeactivation() {
        // Clear caches
        $this->container->get('cache_service')->clearAll();

        // Flush rewrite rules
        flush_rewrite_rules();

        do_action('dll_plugin_deactivated', $this);
    }

    /**
     * Check if Divi is available
     *
     * @return bool
     */
    private function isDiviAvailable() {
        return defined('ET_BUILDER_VERSION') && function_exists('et_core_portability_register');
    }

    /**
     * Show Divi required notice
     */
    public function showDiviRequiredNotice() {
        $message = sprintf(
            '<div class="notice notice-error"><p><strong>%s</strong> %s</p></div>',
            esc_html__('Divi Layout Library:', 'divi-layout-library'),
            esc_html__('This plugin requires Divi theme or Divi Builder plugin to be installed and activated.', 'divi-layout-library')
        );
        echo wp_kses_post($message);
    }

    /**
     * Create database tables
     */
    private function createDatabaseTables() {
        // Implementation for custom tables if needed
        // For now, we'll use WordPress post types and meta
    }

    /**
     * Set default plugin options
     */
    private function setDefaultOptions() {
        $defaults = [
            'dll_version' => $this->version,
            'dll_cache_duration' => 12 * HOUR_IN_SECONDS,
            'dll_max_import_size' => 10 * MB_IN_BYTES,
        ];

        foreach ($defaults as $option => $value) {
            if (!get_option($option)) {
                add_option($option, $value);
            }
        }
    }

    /**
     * Get container instance
     *
     * @return Container
     */
    public function getContainer() {
        return $this->container;
    }

    /**
     * Get plugin version
     *
     * @return string
     */
    public function getVersion() {
        return $this->version;
    }

    /**
     * Get plugin path
     *
     * @return string
     */
    public function getPluginPath() {
        return $this->plugin_path;
    }

    /**
     * Get plugin URL
     *
     * @return string
     */
    public function getPluginUrl() {
        return $this->plugin_url;
    }

    /**
     * Get main plugin file path
     *
     * @return string
     */
    private function getPluginFile() {
        return $this->plugin_path . 'divi-layout-library.php';
    }
}