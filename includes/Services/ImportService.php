<?php

namespace DiviLayoutLibrary\Services;

use DiviLayoutLibrary\Utilities\Validator;
use DiviLayoutLibrary\Utilities\Sanitizer;
use DiviLayoutLibrary\Utilities\Logger;

/**
 * Import Service
 *
 * Handles layout imports from various sources with media processing
 */
class ImportService {

    /**
     * @var MediaService
     */
    private $media_service;

    /**
     * @var Validator
     */
    private $validator;

    /**
     * @var Sanitizer
     */
    private $sanitizer;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * Constructor
     *
     * @param MediaService $media_service
     * @param Validator $validator
     * @param Sanitizer $sanitizer
     * @param Logger $logger
     */
    public function __construct(
        MediaService $media_service,
        Validator $validator,
        Sanitizer $sanitizer,
        Logger $logger
    ) {
        $this->media_service = $media_service;
        $this->validator = $validator;
        $this->sanitizer = $sanitizer;
        $this->logger = $logger;
    }

    /**
     * Import layout from data
     *
     * @param array|string $layout_data Layout data (JSON string or array)
     * @param array $options Import options
     * @return array Result with success status and data/message
     */
    public function importLayout($layout_data, $options = []) {
        try {
            // Parse layout data
            $parsed_data = $this->parseLayoutData($layout_data);
            if (!$parsed_data['success']) {
                return $parsed_data;
            }

            $data = $parsed_data['data'];

            // Validate layout data structure
            if (!$this->validateLayoutStructure($data)) {
                return [
                    'success' => false,
                    'message' => 'Invalid layout data structure'
                ];
            }

            // Process import based on type
            $import_type = $options['import_type'] ?? 'library';

            switch ($import_type) {
                case 'page':
                    return $this->importAsPage($data, $options);
                case 'library':
                default:
                    return $this->importToLibrary($data, $options);
            }

        } catch (\Exception $e) {
            $this->logger->error('Import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import layout from URL
     *
     * @param string $url
     * @param array $options
     * @return array Result with success status and data/message
     */
    public function importFromUrl($url, $options = []) {
        try {
            // Validate URL
            if (!$this->validator->isValidUrl($url)) {
                return [
                    'success' => false,
                    'message' => 'Invalid URL provided'
                ];
            }

            // Fetch data from URL
            $response = \wp_remote_get($url, [
                'timeout' => 30,
                'headers' => [
                    'User-Agent' => 'Divi Layout Library/2.0'
                ]
            ]);

            if (\is_wp_error($response)) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch data from URL: ' . $response->get_error_message()
                ];
            }

            $body = \wp_remote_retrieve_body($response);
            if (empty($body)) {
                return [
                    'success' => false,
                    'message' => 'No data received from URL'
                ];
            }

            // Import the fetched data
            return $this->importLayout($body, $options);

        } catch (\Exception $e) {
            $this->logger->error('URL import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'URL import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Parse layout data from various formats
     *
     * @param mixed $layout_data
     * @return array Result with success status and parsed data
     */
    private function parseLayoutData($layout_data) {
        try {
            // If it's already an array, return as is
            if (\is_array($layout_data)) {
                return [
                    'success' => true,
                    'data' => $layout_data
                ];
            }

            // If it's a string, try to decode as JSON
            if (\is_string($layout_data)) {
                $decoded = \json_decode($layout_data, true);

                if (\json_last_error() === JSON_ERROR_NONE) {
                    return [
                        'success' => true,
                        'data' => $decoded
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Invalid JSON data: ' . \json_last_error_msg()
                ];
            }

            return [
                'success' => false,
                'message' => 'Unsupported data format'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to parse layout data: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate layout data structure
     *
     * @param array $data
     * @return bool
     */
    private function validateLayoutStructure($data) {
        // Check for required Divi portability structure
        if (!isset($data['context']) || $data['context'] !== 'et_builder') {
            return false;
        }

        if (!isset($data['data']) || !\is_array($data['data'])) {
            return false;
        }

        return true;
    }

    /**
     * Import layout to Divi Library
     *
     * @param array $data
     * @param array $options
     * @return array Result with success status and data/message
     */
    private function importToLibrary($data, $options = []) {
        try {
            // Use Divi's portability system
            if (!\function_exists('et_core_portability_import')) {
                return [
                    'success' => false,
                    'message' => 'Divi portability system not available'
                ];
            }

            // Process media in content
            $processed_data = $this->processMediaInContent($data);

            // Import using Divi's system
            $result = \et_core_portability_import($processed_data, false);

            if (\is_wp_error($result)) {
                return [
                    'success' => false,
                    'message' => 'Divi import failed: ' . $result->get_error_message()
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'import_result' => $result,
                    'processed_data' => $processed_data
                ],
                'message' => 'Layout imported to library successfully'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Library import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Library import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import layout as new page
     *
     * @param array $data
     * @param array $options
     * @return array Result with success status and data/message
     */
    private function importAsPage($data, $options = []) {
        try {
            $page_title = $options['page_title'] ?? 'Imported Layout';
            $page_status = $options['page_status'] ?? 'draft';

            // Process media in content
            $processed_data = $this->processMediaInContent($data);

            // Extract content from processed data
            $content = '';
            if (isset($processed_data['data']['postContent'])) {
                $content = $processed_data['data']['postContent'];
            } elseif (isset($processed_data['data'][0]['postContent'])) {
                $content = $processed_data['data'][0]['postContent'];
            }

            if (empty($content)) {
                return [
                    'success' => false,
                    'message' => 'No content found in layout data'
                ];
            }

            // Create new page
            $page_data = [
                'post_type' => 'page',
                'post_title' => \sanitize_text_field($page_title),
                'post_content' => $content,
                'post_status' => \sanitize_key($page_status),
                'post_author' => \get_current_user_id(),
                'meta_input' => [
                    '_et_pb_use_builder' => 'on',
                    '_et_pb_page_layout' => 'et_full_width_page'
                ]
            ];

            $page_id = \wp_insert_post($page_data, true);

            if (\is_wp_error($page_id)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create page: ' . $page_id->get_error_message()
                ];
            }

            // Apply global presets if available
            if (isset($processed_data['data']['presets'])) {
                $this->applyGlobalPresets($processed_data['data']['presets']);
            }

            return [
                'success' => true,
                'data' => [
                    'page_id' => $page_id,
                    'page_title' => $page_title,
                    'page_status' => $page_status,
                    'edit_url' => \admin_url('post.php?post=' . $page_id . '&action=edit'),
                    'view_url' => \get_permalink($page_id)
                ],
                'message' => 'Page created successfully with imported layout'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Page import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Page import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process media in layout content
     *
     * @param array $data
     * @return array Processed data with updated media URLs
     */
    private function processMediaInContent($data) {
        // This would process and replace media URLs in the content
        // For now, return data as-is
        // TODO: Implement media processing similar to competitor plugin
        return $data;
    }

    /**
     * Apply global presets from imported data
     *
     * @param array $presets
     * @return bool
     */
    private function applyGlobalPresets($presets) {
        try {
            if (empty($presets) || !\is_array($presets)) {
                return false;
            }

            // Apply presets using Divi's system if available
            if (\function_exists('et_core_portability_import_global_presets')) {
                return \et_core_portability_import_global_presets($presets);
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Failed to apply global presets: ' . $e->getMessage());
            return false;
        }
    }
}