<?php

namespace DiviLayoutLibrary\Services;

/**
 * Portability Service Class
 *
 * Wraps Divi's ET_Core_Portability class for import/export operations
 */
class PortabilityService {

    /**
     * Import layout data using <PERSON><PERSON>'s portability system
     *
     * @param array $layout_data The layout data to import
     * @return array Result array with success status and data/message
     */
    public function import_layout($layout_data) {
        try {
            // Check if <PERSON><PERSON> is active and ET_Core_Portability class exists
            if (!\class_exists('ET_Core_Portability')) {
                return [
                    'success' => false,
                    'message' => 'Divi theme is not active or ET_Core_Portability class not found'
                ];
            }

            // Validate layout data
            if (empty($layout_data)) {
                return [
                    'success' => false,
                    'message' => 'Invalid layout data provided'
                ];
            }

            // Determine the correct portability context based on the layout data
            $portability_context = $this->determine_portability_context($layout_data);

            // Register the portability context if not already registered
            $this->ensure_portability_context_registered($portability_context);

            // Create temporary file for import
            $temp_file = $this->create_temp_import_file($layout_data);
            if (!$temp_file) {
                return [
                    'success' => false,
                    'message' => 'Failed to create temporary import file'
                ];
            }

            // Set up $_FILES array for Divi's import process
            $_FILES['file'] = [
                'name' => 'layout_import.json',
                'type' => 'application/json',
                'tmp_name' => $temp_file,
                'error' => 0,
                'size' => \filesize($temp_file)
            ];

            // Initialize Divi's portability system for layouts
            $portability = new \ET_Core_Portability($portability_context);

            // Perform the import
            $import_result = $portability->import('sideload');

            // Clean up temporary file
            if (\file_exists($temp_file)) {
                \unlink($temp_file);
            }

            // Process import result
            if (\is_array($import_result) && !isset($import_result['message'])) {
                return [
                    'success' => true,
                    'data' => $import_result,
                    'message' => 'Layout imported successfully'
                ];
            } else {
                $error_message = 'Import failed';
                if (\is_array($import_result) && isset($import_result['message'])) {
                    $error_message = $this->get_error_message($import_result['message']);
                }

                return [
                    'success' => false,
                    'message' => $error_message
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Import error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export layout data using Divi's portability system
     *
     * @param int $layout_id The layout post ID to export
     * @param string $export_name Optional name for the export
     * @return array Result array with success status and data/message
     */
    public function export_layout($layout_id, $export_name = '') {
        try {
            // Check if Divi is active and ET_Core_Portability class exists
            if (!\class_exists('ET_Core_Portability')) {
                return [
                    'success' => false,
                    'message' => 'Divi theme is not active or ET_Core_Portability class not found'
                ];
            }

            // Validate layout ID
            $layout_id = \intval($layout_id);
            if ($layout_id <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid layout ID provided'
                ];
            }

            // Check if post exists and user can edit it
            $post = \get_post($layout_id);
            if (!$post) {
                return [
                    'success' => false,
                    'message' => 'Layout not found'
                ];
            }

            if (!\current_user_can('edit_post', $layout_id)) {
                return [
                    'success' => false,
                    'message' => 'Insufficient permissions to export this layout'
                ];
            }

            // Register the et_builder portability context if not already registered
            $this->ensure_portability_context_registered();

            // Get post content
            $post_content = $post->post_content;
            if (empty($post_content)) {
                return [
                    'success' => false,
                    'message' => 'Layout has no content to export'
                ];
            }

            // Set up POST data for Divi's export process
            $_POST['post'] = $layout_id;
            $_POST['content'] = $post_content;

            // Initialize Divi's portability system for layouts
            $portability_context = 'et_builder';
            $portability = new \ET_Core_Portability($portability_context);

            // Perform the export
            $export_result = $portability->export(true);

            // Process export result
            if (\is_array($export_result) && isset($export_result['data'])) {
                // Generate filename
                $filename = $export_name ?: $post->post_title ?: 'divi_layout';
                $filename = \sanitize_file_name($filename);

                return [
                    'success' => true,
                    'data' => [
                        'export_data' => $export_result,
                        'filename' => $filename,
                        'timestamp' => $export_result['timestamp'] ?? \time()
                    ],
                    'message' => 'Layout exported successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Export failed - invalid result from Divi portability system'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Export error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create temporary file for import process
     *
     * @param array $layout_data The layout data to write to file
     * @return string|false Path to temporary file or false on failure
     */
    private function create_temp_import_file($layout_data) {
        try {
            // Create temporary file
            $temp_file = \wp_tempnam('divi_layout_import');
            if (!$temp_file) {
                return false;
            }

            // Convert layout data to JSON
            $json_data = \wp_json_encode($layout_data);
            if ($json_data === false) {
                return false;
            }

            // Write JSON data to temporary file
            $bytes_written = \file_put_contents($temp_file, $json_data);
            if ($bytes_written === false) {
                return false;
            }

            return $temp_file;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Determine the correct portability context based on layout data
     *
     * @param array $layout_data The layout data to analyze
     * @return string The appropriate portability context
     */
    private function determine_portability_context($layout_data) {
        // Check if the layout data has a context field
        if (isset($layout_data['context'])) {
            return $layout_data['context'];
        }

        // Default to et_builder for individual layouts
        return 'et_builder';
    }

    /**
     * Ensure the specified portability context is registered
     *
     * @param string $context The portability context to register
     * @return void
     */
    private function ensure_portability_context_registered($context = 'et_builder') {
        // Check if the context is already registered
        if (\function_exists('et_core_cache_get')) {
            $instance = \et_core_cache_get($context, 'et_core_portability');
            if ($instance) {
                return; // Already registered
            }
        }

        // Register the portability context
        if (\function_exists('et_core_portability_register')) {
            $context_configs = [
                'et_builder' => [
                    'name' => 'Divi Builder',
                    'view' => false,
                ],
                'et_builder_layouts' => [
                    'name' => 'Divi Library',
                    'view' => false,
                ],
            ];

            $config = $context_configs[$context] ?? $context_configs['et_builder'];
            \et_core_portability_register($context, $config);
        }
    }

    /**
     * Get user-friendly error message from Divi error codes
     *
     * @param string $error_code The error code from Divi
     * @return string User-friendly error message
     */
    private function get_error_message($error_code) {
        $error_messages = [
            'invalideFile' => 'Invalid file format. Please upload a valid Divi layout JSON file.',
            'importContextFail' => 'Import context mismatch. This file is not compatible with layout import.',
            'default' => 'Import failed. Please check the layout file and try again.'
        ];

        return $error_messages[$error_code] ?? $error_messages['default'];
    }
}
