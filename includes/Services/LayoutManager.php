<?php

namespace DiviLayoutLibrary\Services;

/**
 * Layout Manager Class
 * 
 * Handles layout data management, validation, and file operations
 */
class LayoutManager {
    
    /**
     * @var string Option name for storing layout data
     */
    private $option_name = 'dll_layouts_data';
    
    /**
     * @var PortabilityService
     */
    private $portability_service;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->portability_service = new PortabilityService();
    }

    /**
     * Create a new page with imported layout
     * 
     * @param string $page_title The title for the new page
     * @param array $layout_data The layout data to import
     * @param string $page_status The page status (draft, publish, etc.)
     * @return array Result array with success status and data/message
     */
    public function create_page_with_layout($page_title, $layout_data, $page_status = 'draft') {
        try {
            // Validate inputs
            if (empty($page_title)) {
                return [
                    'success' => false,
                    'message' => 'Page title is required'
                ];
            }

            if (empty($layout_data) || !\is_array($layout_data)) {
                return [
                    'success' => false,
                    'message' => 'Invalid layout data provided'
                ];
            }

            // Sanitize page title and status
            $page_title = \sanitize_text_field($page_title);
            $page_status = \sanitize_key($page_status);
            
            // Validate page status
            $valid_statuses = ['draft', 'publish', 'private', 'pending'];
            if (!\in_array($page_status, $valid_statuses)) {
                $page_status = 'draft';
            }

            // First import the layout to get the content
            $import_result = $this->portability_service->import_layout($layout_data);
            
            if (!$import_result['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to import layout: ' . $import_result['message']
                ];
            }

            // Extract the post content from import result
            $post_content = '';
            if (isset($import_result['data']['postContent'])) {
                $post_content = $import_result['data']['postContent'];
            } else {
                return [
                    'success' => false,
                    'message' => 'No content found in imported layout'
                ];
            }

            // Create the new page
            $page_data = [
                'post_title' => $page_title,
                'post_content' => $post_content,
                'post_status' => $page_status,
                'post_type' => 'page',
                'post_author' => \get_current_user_id(),
                'meta_input' => [
                    '_et_pb_use_builder' => 'on',
                    '_et_pb_page_layout' => 'et_full_width_page'
                ]
            ];

            // Insert the page
            $page_id = \wp_insert_post($page_data, true);

            if (\is_wp_error($page_id)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create page: ' . $page_id->get_error_message()
                ];
            }

            // Apply any global presets if they exist
            if (isset($import_result['data']['presets']) && !empty($import_result['data']['presets'])) {
                $this->apply_global_presets($import_result['data']['presets']);
            }

            return [
                'success' => true,
                'data' => [
                    'page_id' => $page_id,
                    'page_title' => $page_title,
                    'page_status' => $page_status,
                    'edit_url' => \admin_url('post.php?post=' . $page_id . '&action=edit'),
                    'view_url' => \get_permalink($page_id)
                ],
                'message' => 'Page created successfully with imported layout'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating page: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get available layouts for export
     * 
     * @return array Result array with success status and data/message
     */
    public function get_available_layouts() {
        try {
            global $wpdb;

            // Query for Divi layouts (posts with Divi builder content)
            $query = "
                SELECT p.ID, p.post_title, p.post_type, p.post_status, p.post_modified
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE pm.meta_key = '_et_pb_use_builder'
                AND pm.meta_value = 'on'
                AND p.post_status IN ('publish', 'draft', 'private')
                AND p.post_type IN ('page', 'post', 'et_pb_layout')
                ORDER BY p.post_modified DESC
                LIMIT 100
            ";

            $results = $wpdb->get_results($query);

            if ($wpdb->last_error) {
                return [
                    'success' => false,
                    'message' => 'Database error: ' . $wpdb->last_error
                ];
            }

            $layouts = [];
            foreach ($results as $result) {
                // Check if user can edit this post
                if (!\current_user_can('edit_post', $result->ID)) {
                    continue;
                }

                $layouts[] = [
                    'id' => (int) $result->ID,
                    'title' => $result->post_title,
                    'type' => $result->post_type,
                    'status' => $result->post_status,
                    'modified' => $result->post_modified,
                    'edit_url' => \admin_url('post.php?post=' . $result->ID . '&action=edit')
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'layouts' => $layouts,
                    'total' => \count($layouts)
                ],
                'message' => 'Layouts retrieved successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving layouts: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get predefined layout data from static JS object
     * 
     * @return array Array of predefined layouts
     */
    public function get_predefined_layouts() {
        // This would typically come from a database or external API
        // For now, returning sample data as per project requirements
        return [
            [
                'id' => 'layout-1',
                'name' => 'Modern Business',
                'category' => 'Business',
                'description' => 'A clean and modern business layout for corporate sites.',
                'previewImage' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'images/previews/business-modern.jpg',
                'previewLink' => 'https://demo.example.com/business-modern',
                'jsonFile' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'Gardener-All-Layouts-Import.json'
            ],
            [
                'id' => 'layout-2',
                'name' => 'Creative Portfolio',
                'category' => 'Portfolio',
                'description' => 'A creative portfolio layout perfect for showcasing work.',
                'previewImage' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'images/previews/portfolio-creative.jpg',
                'previewLink' => 'https://demo.example.com/portfolio-creative',
                'jsonFile' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'layouts/portfolio-creative.json'
            ],
            [
                'id' => 'layout-3',
                'name' => 'Restaurant Menu',
                'category' => 'Restaurant',
                'description' => 'An elegant restaurant layout with menu showcase.',
                'previewImage' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'images/previews/restaurant-menu.jpg',
                'previewLink' => 'https://demo.example.com/restaurant-menu',
                'jsonFile' => DIVI_LAYOUT_LIBARY_ASSETS_URI . 'layouts/restaurant-menu.json'
            ]
        ];
    }

    /**
     * Apply global presets from imported layout
     * 
     * @param array $presets The presets data to apply
     * @return bool Success status
     */
    private function apply_global_presets($presets) {
        try {
            if (empty($presets) || !\is_array($presets)) {
                return false;
            }

            // Use Divi's portability service to import global presets
            if (\method_exists($this->portability_service, 'import_global_presets')) {
                return $this->portability_service->import_global_presets($presets);
            }

            return true;

        } catch (\Exception $e) {
            \error_log('Error applying global presets: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate layout data structure
     * 
     * @param array $layout_data The layout data to validate
     * @return bool True if valid, false otherwise
     */
    public function validate_layout_data($layout_data) {
        if (!\is_array($layout_data)) {
            return false;
        }

        // Check for required fields
        $required_fields = ['context', 'data'];
        foreach ($required_fields as $field) {
            if (!isset($layout_data[$field])) {
                return false;
            }
        }

        // Validate context
        if ($layout_data['context'] !== 'et_builder') {
            return false;
        }

        // Validate data structure
        if (!\is_array($layout_data['data']) || empty($layout_data['data'])) {
            return false;
        }

        return true;
    }
}
