<?php

namespace DiviLayoutLibrary\Admin;

class Menu {
    /**
	 * add plugin menu page and submenu pages
	 */
	public function __construct()
	{
		\add_action('admin_menu', [$this, 'admin_menu']);
	}

	/**
	 * add admin menu page
	 * @return hooks
	 */
	public function admin_menu()
	{
		\add_submenu_page(
			'options-general.php', 'Divi Layout Library', 'Divi Layout Library', 'manage_options', 'divi-layout-library', array(&$this, 'load_main_template')
		);
	}
	public function load_main_template()
	{
		echo '<div id="divi-layout-library-body" class="divi-layout-library-body"></div>';
	}
}