<?php

namespace DiviLayoutLibrary\Admin;

class Ajax {

    /**
     * Constructor - Register AJAX actions
     */
    public function __construct() {
        \add_action('wp_ajax_dll_import_layout', [$this, 'import_layout']);
        \add_action('wp_ajax_dll_export_layout', [$this, 'export_layout']);
        \add_action('wp_ajax_dll_get_layouts', [$this, 'get_layouts']);
        \add_action('wp_ajax_dll_create_page_with_layout', [$this, 'create_page_with_layout']);
    }

    /**
     * Verify nonce and check user capabilities
     *
     * @param string $action The action to verify
     * @return bool
     */
    private function verify_request($action = 'dll_nonce') {
        // Check nonce
        if (!\wp_verify_nonce($_POST['nonce'] ?? '', $action)) {
            \wp_send_json_error(['message' => 'Security check failed']);
            return false;
        }

        // Check user capabilities
        if (!\current_user_can('manage_options')) {
            \wp_send_json_error(['message' => 'Insufficient permissions']);
            return false;
        }

        return true;
    }

    /**
     * Sanitize and validate input data
     *
     * @param array $data Raw input data
     * @param array $rules Validation rules
     * @return array|false Sanitized data or false on failure
     */
    private function sanitize_input($data, $rules) {
        $sanitized = [];

        foreach ($rules as $field => $rule) {
            if ($rule['required'] && !isset($data[$field])) {
                return false;
            }

            if (isset($data[$field])) {
                switch ($rule['type']) {
                    case 'text':
                        $sanitized[$field] = \sanitize_text_field($data[$field]);
                        break;
                    case 'textarea':
                        $sanitized[$field] = \sanitize_textarea_field($data[$field]);
                        break;
                    case 'int':
                        $sanitized[$field] = \intval($data[$field]);
                        break;
                    case 'bool':
                        $sanitized[$field] = (bool) $data[$field];
                        break;
                    case 'json':
                        $decoded = \json_decode($data[$field], true);
                        if (\json_last_error() === JSON_ERROR_NONE) {
                            $sanitized[$field] = $decoded;
                        } else {
                            return false;
                        }
                        break;
                    default:
                        $sanitized[$field] = \sanitize_text_field($data[$field]);
                }
            }
        }

        return $sanitized;
    }

    /**
     * Import layout AJAX handler
     */
    public function import_layout() {
        try {
            if (!$this->verify_request()) {
                return;
            }

            $rules = [
                'layout_data' => ['type' => 'json', 'required' => true],
                'import_type' => ['type' => 'text', 'required' => true], // 'library' or 'page'
            ];

            // $input = $this->sanitize_input($_POST, $rules);

            // error_log( print_r( $_POST['layout_data'], true ) );

            if (!$_POST) {
                \wp_send_json_error(['message' => 'Invalid input data']);
                return;
            }

            // Get the portability service
            $portability = new \DiviLayoutLibrary\Services\PortabilityService();

            // Import the layout
            $result = $portability->import_layout($_POST['layout_data'] );

            if ($result['success']) {
                \wp_send_json_success([
                    'message' => 'Layout imported successfully',
                    'data' => $result['data']
                ]);
            } else {
                \wp_send_json_error([
                    'message' => $result['message'] ?? 'Import failed'
                ]);
            }

        } catch (\Exception $e) {
            \wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    /**
     * Create page with layout AJAX handler
     */
    public function create_page_with_layout() {
        try {
            if (!$this->verify_request()) {
                return;
            }

            $rules = [
                'layout_data' => ['type' => 'json', 'required' => true],
                'page_title' => ['type' => 'text', 'required' => true],
                'page_status' => ['type' => 'text', 'required' => false],
            ];

            $input = $this->sanitize_input($_POST, $rules);
            if (!$input) {
                \wp_send_json_error(['message' => 'Invalid input data']);
                return;
            }

            // Set default page status
            $input['page_status'] = $input['page_status'] ?? 'draft';

            // Get the layout manager service
            $layout_manager = new \DiviLayoutLibrary\Services\LayoutManager();

            // Create page with layout
            $result = $layout_manager->create_page_with_layout(
                $input['page_title'],
                $input['layout_data'],
                $input['page_status']
            );

            if ($result['success']) {
                \wp_send_json_success([
                    'message' => 'Page created successfully',
                    'data' => $result['data']
                ]);
            } else {
                \wp_send_json_error([
                    'message' => $result['message'] ?? 'Page creation failed'
                ]);
            }

        } catch (\Exception $e) {
            \wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    /**
     * Export layout AJAX handler
     */
    public function export_layout() {
        try {
            if (!$this->verify_request()) {
                return;
            }

            $rules = [
                'layout_id' => ['type' => 'int', 'required' => true],
                'export_name' => ['type' => 'text', 'required' => false],
            ];

            $input = $this->sanitize_input($_POST, $rules);
            if (!$input) {
                \wp_send_json_error(['message' => 'Invalid input data']);
                return;
            }

            // Get the portability service
            $portability = new \DiviLayoutLibrary\Services\PortabilityService();

            // Export the layout
            $result = $portability->export_layout($input['layout_id'], $input['export_name'] ?? '');

            if ($result['success']) {
                \wp_send_json_success([
                    'message' => 'Layout exported successfully',
                    'data' => $result['data']
                ]);
            } else {
                \wp_send_json_error([
                    'message' => $result['message'] ?? 'Export failed'
                ]);
            }

        } catch (\Exception $e) {
            \wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    /**
     * Get available layouts AJAX handler
     */
    public function get_layouts() {
        try {
            if (!$this->verify_request()) {
                return;
            }

            // Get the layout manager service
            $layout_manager = new \DiviLayoutLibrary\Services\LayoutManager();

            // Get layouts
            $result = $layout_manager->get_available_layouts();

            if ($result['success']) {
                \wp_send_json_success([
                    'message' => 'Layouts retrieved successfully',
                    'data' => $result['data']
                ]);
            } else {
                \wp_send_json_error([
                    'message' => $result['message'] ?? 'Failed to retrieve layouts'
                ]);
            }

        } catch (\Exception $e) {
            \wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }
}