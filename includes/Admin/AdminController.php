<?php

namespace DiviLayoutLibrary\Admin;

use DiviLayoutLibrary\Core\Container;

/**
 * Admin Controller
 *
 * Coordinates admin functionality and manages admin components
 */
class AdminController {

    /**
     * @var Container
     */
    private $container;

    /**
     * @var MenuManager
     */
    private $menu_manager;

    /**
     * @var AssetManager
     */
    private $asset_manager;

    /**
     * @var AjaxHandler
     */
    private $ajax_handler;

    /**
     * Constructor
     *
     * @param Container $container
     */
    public function __construct(Container $container) {
        $this->container = $container;
        $this->initializeComponents();
        $this->initHooks();
    }

    /**
     * Initialize admin components
     */
    private function initializeComponents() {
        $this->menu_manager = new MenuManager($this->container);
        $this->asset_manager = new AssetManager($this->container);
        $this->ajax_handler = new AjaxHandler($this->container);
    }

    /**
     * Initialize WordPress hooks
     */
    private function initHooks() {
        \add_action('admin_menu', [$this, 'registerMenus']);
        \add_action('admin_enqueue_scripts', [$this, 'enqueueAssets']);
        \add_action('admin_init', [$this, 'handleAdminInit']);
        \add_action('admin_notices', [$this, 'showAdminNotices']);

        // AJAX hooks are handled by AjaxHandler
    }

    /**
     * Register admin menus
     */
    public function registerMenus() {
        $this->menu_manager->registerMenus();
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook
     */
    public function enqueueAssets($hook) {
        $this->asset_manager->enqueueAssets($hook);
    }

    /**
     * Handle admin initialization
     */
    public function handleAdminInit() {
        // Check user capabilities
        if (!\current_user_can('manage_options')) {
            return;
        }

        // Register settings
        $this->registerSettings();

        // Handle admin actions
        $this->handleAdminActions();

        do_action('dll_admin_init', $this);
    }

    /**
     * Show admin notices
     */
    public function showAdminNotices() {
        // Check if Divi is available
        if (!$this->isDiviAvailable()) {
            $this->showDiviRequiredNotice();
            return;
        }

        // Show other notices
        $this->showUpdateNotices();
        $this->showErrorNotices();
    }

    /**
     * Register plugin settings
     */
    private function registerSettings() {
        // Register settings sections and fields
        \register_setting('dll_settings', 'dll_cache_duration', [
            'type' => 'integer',
            'default' => 12 * HOUR_IN_SECONDS,
            'sanitize_callback' => 'absint'
        ]);

        \register_setting('dll_settings', 'dll_max_import_size', [
            'type' => 'integer',
            'default' => 10 * MB_IN_BYTES,
            'sanitize_callback' => 'absint'
        ]);

        \register_setting('dll_settings', 'dll_enable_logging', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);
    }

    /**
     * Handle admin actions
     */
    private function handleAdminActions() {
        // Handle form submissions and other admin actions
        if (isset($_POST['dll_action']) && \wp_verify_nonce($_POST['dll_nonce'], 'dll_admin_action')) {
            $action = \sanitize_key($_POST['dll_action']);

            switch ($action) {
                case 'clear_cache':
                    $this->handleClearCache();
                    break;
                case 'reset_settings':
                    $this->handleResetSettings();
                    break;
            }
        }
    }

    /**
     * Handle clear cache action
     */
    private function handleClearCache() {
        try {
            $cache_service = $this->container->get('cache_service');
            $cache_service->clearAll();

            \add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' .
                     \esc_html__('Cache cleared successfully.', 'divi-layout-library') .
                     '</p></div>';
            });

        } catch (\Exception $e) {
            \add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error is-dismissible"><p>' .
                     \esc_html__('Failed to clear cache: ', 'divi-layout-library') .
                     \esc_html($e->getMessage()) .
                     '</p></div>';
            });
        }
    }

    /**
     * Handle reset settings action
     */
    private function handleResetSettings() {
        try {
            \delete_option('dll_cache_duration');
            \delete_option('dll_max_import_size');
            \delete_option('dll_enable_logging');

            \add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' .
                     \esc_html__('Settings reset to defaults.', 'divi-layout-library') .
                     '</p></div>';
            });

        } catch (\Exception $e) {
            \add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error is-dismissible"><p>' .
                     \esc_html__('Failed to reset settings: ', 'divi-layout-library') .
                     \esc_html($e->getMessage()) .
                     '</p></div>';
            });
        }
    }

    /**
     * Check if Divi is available
     *
     * @return bool
     */
    private function isDiviAvailable() {
        return \defined('ET_BUILDER_VERSION') && \function_exists('et_core_portability_register');
    }

    /**
     * Show Divi required notice
     */
    private function showDiviRequiredNotice() {
        $message = \sprintf(
            '<div class="notice notice-error"><p><strong>%s</strong> %s</p></div>',
            \esc_html__('Divi Layout Library:', 'divi-layout-library'),
            \esc_html__('This plugin requires Divi theme or Divi Builder plugin to be installed and activated.', 'divi-layout-library')
        );
        echo \wp_kses_post($message);
    }

    /**
     * Show update notices
     */
    private function showUpdateNotices() {
        // Check for plugin updates or important announcements
        $last_version = \get_option('dll_last_version', '0.0.0');
        $current_version = $this->container->get('plugin')->getVersion();

        if (\version_compare($last_version, $current_version, '<')) {
            \add_action('admin_notices', function() use ($current_version) {
                echo '<div class="notice notice-info is-dismissible"><p>' .
                     \sprintf(
                         \esc_html__('Divi Layout Library has been updated to version %s. Check out the new features!', 'divi-layout-library'),
                         \esc_html($current_version)
                     ) .
                     '</p></div>';
            });

            \update_option('dll_last_version', $current_version);
        }
    }

    /**
     * Show error notices
     */
    private function showErrorNotices() {
        // Show any stored error messages
        $errors = \get_transient('dll_admin_errors');
        if ($errors && \is_array($errors)) {
            foreach ($errors as $error) {
                \add_action('admin_notices', function() use ($error) {
                    echo '<div class="notice notice-error is-dismissible"><p>' .
                         \esc_html($error) .
                         '</p></div>';
                });
            }
            \delete_transient('dll_admin_errors');
        }
    }

    /**
     * Get container instance
     *
     * @return Container
     */
    public function getContainer() {
        return $this->container;
    }

    /**
     * Get menu manager
     *
     * @return MenuManager
     */
    public function getMenuManager() {
        return $this->menu_manager;
    }

    /**
     * Get asset manager
     *
     * @return AssetManager
     */
    public function getAssetManager() {
        return $this->asset_manager;
    }

    /**
     * Get AJAX handler
     *
     * @return AjaxHandler
     */
    public function getAjaxHandler() {
        return $this->ajax_handler;
    }
}