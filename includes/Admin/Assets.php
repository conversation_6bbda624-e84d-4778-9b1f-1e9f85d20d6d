<?php

namespace DiviLayoutLibrary\Admin;

class Assets {
    private $pages = ['settings_page_divi-layout-library'];
    public function __construct() {
        \add_action('admin_enqueue_scripts', [$this, 'plugin_scripts']);
    }

    public function plugin_scripts($hook) {
        if( in_array($hook, $this->pages) ){
            $dependencies = include_once DIVI_LAYOUT_LIBARY_ASSETS_DIR_PATH . 'js/divi-layout-library.core.min.asset.php';
            \wp_enqueue_style('divi-layout-library-admin', DIVI_LAYOUT_LIBARY_ASSETS_URI . 'css/divi-layout-library.css', [], $dependencies['version'], 'all');
            // Ensure Divi's portability script is available
            if (\function_exists('et_core_load_component')) {
                \et_core_load_component(['portability']);
            }

            // \wp_enqueue_script(
            //     'dll-portability',
            //     DIVI_LAYOUT_LIBARY_ASSETS_URI . 'lib/dll-portability.js',
            //     ['jquery'],
            //     $dependencies['version'],
            //     true
            // );
            \wp_enqueue_script(
                'divi-layout-library',
                DIVI_LAYOUT_LIBARY_ASSETS_URI . 'js/divi-layout-library.core.min.js',
                array_merge($dependencies['dependencies'], ['regenerator-runtime', 'jquery']),
                $dependencies['version'],
                true
            );

            // Localize script for AJAX
            \wp_localize_script('divi-layout-library', 'dllAjax', [
                'ajaxUrl' => \admin_url('admin-ajax.php'),
                'nonce' => \wp_create_nonce('dll_nonce'),
                'portability_nonce' => wp_create_nonce('et_core_portability_import'),
                'plugin_root_url' => DIVI_LAYOUT_LIBARY_ROOT_DIR_URL,
                'strings' => [
                    'importing' => \__('Importing layout...', 'divi-layout-library'),
                    'exporting' => \__('Exporting layout...', 'divi-layout-library'),
                    'success' => \__('Success!', 'divi-layout-library'),
                    'error' => \__('An error occurred', 'divi-layout-library'),
                    'invalidFile' => \__('Invalid file format', 'divi-layout-library'),
                    'confirmExport' => \__('Are you sure you want to export this layout?', 'divi-layout-library'),
                    'selectLayout' => \__('Please select a layout to export', 'divi-layout-library'),
                    'pageNameRequired' => \__('Page name is required', 'divi-layout-library'),
                ]
            ]);
        }
    }
}
