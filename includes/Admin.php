<?php

namespace DiviLayoutLibrary;

class Admin {
    public function __construct() {
        $this->add_menu();
        $this->add_assets();
        $this->add_ajax();
        $this->register_portability();
    }

    private function add_menu(){
        new Admin\Menu;
    }

    private function add_assets(){
        new Admin\Assets;
    }

    private function add_ajax(){
        new Admin\Ajax;
    }

    private function register_portability(){
        // Register portability context on admin_init to ensure Divi core is loaded
        // \add_action('init', [$this, 'register_divi_portability_context']);
        $this->register_divi_portability_context();
    }

    public function register_divi_portability_context() {
        // Only register if Divi functions are available
        if (!\function_exists('et_core_portability_register')) {
            return;
        }
        // require_once DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH . 'core/components/Portability.php';
        // Register both portability contexts for our plugin
        \et_core_portability_register('et_builder', [
            'name' => 'Divi Builder',
            'view' => false, // We don't need the UI, just the functionality
        ]);

        \et_core_portability_register('et_builder_layouts', [
            'name' => 'Divi Library',
            'view' => false, // We don't need the UI, just the functionality
        ]);
    }
}
