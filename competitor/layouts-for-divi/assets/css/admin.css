@font-face {
    font-family: 'Nunito';
    src: url('../fonts/nunito/Nunito-Regular.woff2') format('woff2'),
        url('../fonts/nunito/Nunito-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Nunito';
    src: url('../fonts/nunito/Nunito-Bold.woff2') format('woff2'),
        url('../fonts/nunito/Nunito-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
}
@font-face {
    font-family: 'Nunito';
    src: url('../fonts/nunito/Nunito-SemiBold.woff2') format('woff2'),
        url('../fonts/nunito/Nunito-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
}
a {
    text-decoration:none;
}
img {
    max-width:100%;
}

.toastify-right{
    top:280px !important;
}

.lfd-body {
    font-family: 'Nunito';
    font-weight: normal;
    background: #FFFFFF;
}
.lfd-header {
    background: #FFFFFF;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    padding: 32px 50px;
}
.lfd-header img{
    display: inline-block;
    margin-right:10px;
}
.lfd-header h1 {
    padding: 0px;
    margin: 0px;
    line-height: 32px;
    font-family: Nunito;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 26px;
    color: #111111;
    display:inline-block;
}
.envato-elements__collections-header__subnav-title {
    font-family: Nunito;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 20px;
    color: #111111;
}
.lfd-wrap {
    padding: 30px 50px;
}
.collection-bar {
    background: #FFFFFF;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    width: 100%;
    display: table;
    margin-bottom: 50px;
    margin-top: 50px;
}
.collection-bar h4 {
    float: left;
    font-family: Nunito;
    font-style: normal;
    font-weight: normal;
    line-height: normal;
    font-size: 16px;
    position: relative;
    color: #444444;
    padding-left: 20px;
    margin: 20px 30px 20px 50px;
}
.collection-bar h4:after {
    position: absolute;
    content: "";
    width: 10px;
    height: 10px;
    left: 0px;
    top: 6px;
    border-radius: 50%;
    background: #6359F1;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}
.collection-bar .collection-list {
    float: left;
    padding: 0px;
    margin: 0px;
}
.collection-bar .collection-list li {
    display: inline-block;
    margin-bottom: 0px;
    position: relative;
    float: left;
}
.collection-bar .collection-list li a {
    display: block;
    padding: 20px;
    font-family: Nunito;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 16px;
    color: #444444;
    border-left: 1px solid #E9E8EB;
}
.collection-bar .collection-list li a:hover {
    background-image: -moz-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    background-image: -webkit-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    color: #fff;
}
.collection-bar .collection-list li a.active {
    background-image: -moz-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    background-image: -webkit-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    color: #fff;
}
.collection-bar .collection-list li a:focus {
    outline: none;
    box-shadow: none;
}
.list-subnav {
    display: none;
    position: absolute;
    top: 62px;
    left: 0px;
    padding: 0px;
    margin: 0px;
    background: #fff;
    z-index: 99;
    width: 180px;
}
.list-subnav li {
    display: block !important;
    width: 100%;
}
.list-subnav li a {
    display: block !important;
    padding: 10px 20px !important;
}
.collection-list li:hover .list-subnav {
    display: block;
}
.lfd_wrapper {
    width: 100%;
    display: table;
}
.lfd_box {
    color: #fff;
    font-size: 150%;
    width: 25%;
    float: left;
    margin-bottom: 25px;
}
.lfd_box_widget {
    margin-left: 10px;
    margin-right: 10px;
}
.lfd-media {
    position: relative;
    background: #FFFFFF;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    height: 305px;
    overflow: hidden;
}
.pro-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    background: #A662F4;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    width: 50px;
    height: 28px;
    color: #FFFFFF;
    font-family: Nunito;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 14px;
    text-align: center;
    line-height: 28px;
    text-transform: uppercase;
}
.free-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    background: #6BA900;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    width: 50px;
    height: 28px;
    color: #FFFFFF;
    font-family: Nunito;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 14px;
    text-align: center;
    line-height: 28px;
    text-transform: uppercase;
}
.lfd-btn {
    text-align: center;
    margin: 20px 0px;
}
.lfd-btn .btn {
    font-family: Nunito;
    font-style: normal;
    font-weight:600;
    line-height: normal;
    font-size: 16px;
    color: #fff;
    display: inline-block;
    width: 110px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    margin: 0px 5px;
}
.lfd-btn .pre-btn {
    background-image: -moz-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 99, 89, 241 ) 100%);
    background-image: -webkit-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
}
.lfd-btn .pre-btn:hover {
    background: linear-gradient(180deg, rgba(17, 17, 17, 0.8) 0%, #111111 100%);
}
.lfd-btn .ins-btn {
    background-image: -moz-linear-gradient( 135deg, rgb( 49, 181, 251 ) 0%, rgb( 99, 89, 241 ) 100%);
    background-image: -webkit-linear-gradient( 135deg, rgb( 49, 181, 251 ) 0%, rgb( 99, 89, 241 ) 100%);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
}
.lfd-btn .ins-btn:hover {
    background: linear-gradient(180deg, rgba(17, 17, 17, 0.8) 0%, #111111 100%);
}
.lfd-is-inline {
    display: inline-block;
}
.lfd-sync {
    float: right;
    color: #23282d;
    font-size: 1.3em;
    margin: 1em 0;
}
.lfd-sync-btn {
    background-image: -webkit-linear-gradient( 135deg, rgb( 99, 89, 241 ) 0%, rgb( 49, 181, 251 ) 100%);
    color: #fff;
    padding: 10px 25px;
    border-radius: 100px;
}
.lfd-sync-btn:hover,
.lfd-sync-btn:focus {
    color: #fff;
}
.lfd-sync-btn:focus {
    -moz-box-shadow:none;
    box-shadow: none;
}
.lfd-disabled {
    cursor: no-drop;
    pointer-events: none;
}
.lfd-disabled:hover {
    cursor: no-drop;
    pointer-events: none;
}
body #TB_window {
    font-family: 'Nunito';
    font-weight: normal;
    position: fixed;
    background-color: #fff;
    z-index: 100050;
    visibility: hidden;
    text-align: center;
    top: 4%;
    left: 2%;
    width: 96% !important;
    height: 94vh !important;
    -webkit-box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
    box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
    right: 2%;
    margin-top: 0 !important;
    margin-left: 0 !important;
    overflow: auto;
}
body #TB_title {
    background: linear-gradient(160deg, #f755df, #A662F4);
    color: #fff;
    border-bottom: 1px solid #ddd;
    height: 50px;
}
body #TB_closeWindowButton:focus {
    outline: none;
    box-shadow: none !important;
    border-color: transparent;
}
body .tb-close-icon {
    width: 35px;
    height: 35px;
    line-height: 38px;
    background: #fff;
    border-radius: 50%;
    color: #000 !important;
    text-align: center;
    font-size: 24px;
    margin-top: 6px;
    margin-right: 6px;
}
body #TB_ajaxWindowTitle {
    padding: 10px 15px;
    font-size: 22px;
    text-align: left;
    text-transform: capitalize;
}
body #TB_ajaxContent {
    text-align: center;
    height: 86vh !important;
    width: 100% !important;
    display: inline-block;
    overflow: initial;
    padding: 0px;
}
.lfd-control-active:focus,
.lfd-control-active:active {
    outline: 0;
}
.lfd_filter {
    transition: all 3s ease-in-out;
    -webkit-transition: all 3s ease-in-out;
    -moz-transition: all 3s ease-in-out;
    -webkit-transition: all 3s ease-in-out;
    -o-transition: all 3s ease-in-out;
}
.lfd-import-div {
    position: absolute;
    top: 8px;
    left: 18%;
}
.lfd-import,
.lfd-create-page,
.lfd-buy {
    display: inline-block;
    margin: 0px 15px;
}
.lfd-import p,
.lfd-create-page p,
.lfd-buy p {
    display: inline-block;
    color: #fff;
    margin: 0px 15px;
    font-size: 14px;
}
.lfd-create-page p {
    margin-left: 0px;
    margin-right: 0px;
}
.lfd-import-div span {
    display: inline-block;
    color: #fff;
    font-weight: bold;
    margin: 0px 15px;
    font-size: 14px;
}
.lfd-import .ins-btn {
    display: inline-block;
    color: #fff;
    background: linear-gradient(180deg, rgba(17, 17, 17, 0.8) 0%, #111111 100%);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
}
.lfd-import .ins-btn:hover {
    box-shadow: none;
    color: #000;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, #fff 100%);
}
.lfd-impo-tem-btn:focus {
    color: #fff;
    box-shadow: none;
}
.lfd-import-div input {
    margin: 0px 15px;
    padding: 6px 16px;
}
.lef-required {
    border: 1px solid red !important;
}
/** Install popup css start **/
body.install-popup {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.lfd-install-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
}
.lfd-install-popup.on {
    display: block;
}
.lfd-install-header {
    background-image: -moz-linear-gradient( -13deg, rgb( 0, 35, 156 ) 2.148%, rgb( 104, 118, 252 ) 100%);
    background-image: -webkit-linear-gradient( -13deg, rgb( 0, 35, 156 ) 2.148%, rgb( 104, 118, 252 ) 100%);
    color: #fff;
    border-bottom: 1px solid #ddd;
    height: 50px;
}
.lfd-install-title {
    padding: 16px 15px;
    font-size: 22px;
    text-align: left;
    text-transform: capitalize;
}
.lfd-close-icon {
    width: 35px;
    height: 35px;
    line-height: 38px;
    background: #fff;
    border-radius: 50%;
    color: #000 !important;
    text-align: center;
    font-size: 24px;
    margin-top: 6px;
    margin-right: 6px;
    position: absolute;
    right: 4px;
    top: 2px;
    cursor: pointer;
}
.lfd-close-icon:before {
    content: "\f158";
    font: normal 20px/29px dashicons;
    speak: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
body .tb-close-icon:hover,
.lfd-close-icon:hover {
    background: linear-gradient(180deg, rgba(17, 17, 17, 0.8) 0%, #111111 100%);
    color: #fff !important;
}
.lfd-container {
    position: relative;
    display: block;
    top: 20%;
    width: 500px;
    margin: 0 auto;
}
.lfd-install-content {
    position: relative;
    display: block;
    background-color: #fff;
    overflow-y: auto;
    height: 370px;
    padding: 20px;
}
.lfd-install-content p {
    font-size: 18px;
    text-align: center;
}
.lfd-install-popup .lfd-btn .btn {
    display: block;
    margin: 0 auto;
    width: 36%;
}
.lfd-install-popup input {
    margin: 0 auto;
    padding: 6px 16px;
    display: block;
}
.lfd-horizontal {
    position: relative;
}
.lfd-horizontal:before {
    position: absolute;
    content: "";
    width: 80px;
    height: 1px;
    background-color: #000;
    left: 125px;
    top: 13px;
}
.lfd-horizontal:after {
    position: absolute;
    content: "";
    width: 80px;
    height: 1px;
    background-color: #000;
    right: 125px;
    top: 13px;
}
.lfd-dashicons-link {
    position: absolute;
    top: 10px;
    right: 90px;
    color: #fff;
}
.lfd-dashicons {
    display: inline-block !important;
    width: 34px;
    height: 34px;
    font-size: 25px !important;
    line-height: 34px;
    font-family: dashicons;
    text-decoration: inherit;
    font-weight: 400 !important;
    font-style: normal;
    vertical-align: top;
    text-align: center;
    transition: color .1s ease-in;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.lfd-dashicons:before {
    content: "\f504";
}
.lfd-dashicons-link:focus {
    outline: none;
    box-shadow: none;
}
.lfd-dashicons-link:hover {
    color: #111;
}
.lfd-template-title {
    text-align: center;
    background-color: #333;
    color: #fff;
    padding: 10px 0px;
    font-size: 16px;
}
.lfd-error {
    color: #a94442;
}
/** Install popup css end **/

/** preview popup css start **/
body.preview-popup {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.lfd-preview-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
}
.lfd-preview-popup.on {
    display: block;
}
.lfd-preview-container {
    position: relative;
    display: block;
    top: 5%;
    width: 95%;
    margin: 0 auto;
    height: 85%;
    background: #fff;
}
.lfd-preview-header {
    background-image: -moz-linear-gradient( -13deg, rgb( 0, 35, 156 ) 2.148%, rgb( 104, 118, 252 ) 100%);
    background-image: -webkit-linear-gradient( -13deg, rgb( 0, 35, 156 ) 2.148%, rgb( 104, 118, 252 ) 100%);
    color: #fff;
    padding: 10px 15px;
}
.lfd-preview-title {
    font-size: 22px;
    text-align: left;
    text-transform: capitalize;
    display: inline-block
}

.lfd-preview-popup .ins-btn {
    display: inline-block;
    color: #fff;
    background: linear-gradient(180deg, rgba(17, 17, 17, 0.8) 0%, #111111 100%);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
}
.lfd-preview-popup .ins-btn:hover {
    box-shadow: none;
    color: #000;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, #fff 100%);
}
.lfd-preview-popup input {
    margin: 0px 15px;
    padding: 6px 16px;
}

.lfd-gradient-loader {
    display: inline-block;
    background: repeating-linear-gradient(to right, #775cfb 0%, #1ee2fc 50%, #775cfb 100%);
    margin-bottom: -5px;
    width: 100px;
    height:15px;
    border-radius: 34px;
    background-size: 200% auto;
    background-position: 0 100%;
    animation: gradient 3s infinite;
    animation-fill-mode: forwards;
    animation-timing-function: linear;
}

@keyframes gradient {
    0%   { background-position: 0 0; }
    100% { background-position: -200% 0; }
}

/* Media Query */
@media screen and (max-width: 1200px) {
    .lfd_box {
        width: 33.3333%;
    }
    .lfd-wrap {
        padding: 30px 15px;
    }
}

@media screen and (max-width: 1024px){
    .lfd-preview-header {
        text-align: left;
    }
    .lfd-page-create {
        padding-top: 20px;
        padding-bottom: 10px;
    }
    .lfd-btn .btn {
        width: 92px;
        margin: 0px;
    }
    .collection-bar .collection-list {
        float: none;
    }
    .collection-bar .collection-list li a {
        padding: 10px;
    }
    .lfd-page-create p, .lfd-preview-header p {
        display: none !important;
    }
    .lfd-dashicons {
        line-height: 68px;
    }
}

@media screen and (max-width: 992px){
    .lfd-btn .btn {
        width: 92px;
        margin: 0px;
    }
    .lfd-preview-header {
        text-align: left;
    }
    .lfd-page-create {
        display: block;
        padding-top: 20px;
        padding-bottom: 20px;
    }
}

@media screen and (max-width: 767px) {
    .lfd_box {
        width: 50%;
    }
    .lfd-btn .btn {
        width: 92px;
        margin: 0px;
    }
}

@media screen and (max-width: 480px) {
    .lfd_box {
        width: 100%;
    }
    .lfd-btn .btn {
        width: 92px;
        margin: 0px;
    }
}
