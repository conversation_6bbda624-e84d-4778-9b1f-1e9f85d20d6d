=== Layouts for Divi ===
Contributors: techeshta, alkesh7, vastarpara, hadihirpara
Tags: divi layouts, divi layouts addon, free divi layouts, divi layouts module, divi website templates, free divi templates, landing page templates, free divi layouts packs 
Requires at least: 4.4
Tested up to: 6.8
Requires PHP: 8.0
Stable tag: 1.1.2
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Layouts for Divi is a beautifully designed free layout for famous WordPress Divi page builders.

== Description ==

Layouts for Divi is an effective and user-friendly way to create a one-page WordPress website. A Divi addon gives you a top-niche ready-made landing page or layouts for your favorite WordPress Divi page builder. It is a flexible and straightforward way to import your WordPress site via one-click 'Import Template' or 'Create New Page.'
 
Layouts for Divi plugin comes with precisely designed 30+ layouts for all types of start-up businesses. It is a one-stop solution for your one-page professional WordPress websites. You can build a store that matches your brand.
 
It is the perfect choice for your business. Users will love your site because it gives them a unique user experience (UX) and User Interface (UI). Whether you are a beginner, WordPress user, designer, or developer, it's a user-friendly Layout plugin. No additional coding skills are required.

<strong>[Live Demo](https://layoutsfordivibuilder.com/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivibuilder)</strong>

### Where Are Layouts for Divi Plugin Useful?

Layouts for Divi plugin suitable for all kinds of one-page WordPress websites. Below is one small list for which purposes you can use the Layouts for Divi WordPress plugin.

* Education Related Website
* Fitness Related Website
* Beauty Related Website
* Fashion Related Website
* Hotel Business Related Website
* Digital Agency Website
* Personal Portfolio Website
* E-commerce Related Website
* Traveling Website
* Landing Page Website
* Photography Website
* Wedding Website
* Cleaning Services Website
* Etc.
 
### Precisely Designed 30+ Layouts
 
We have designed a stunning landing page for you to complete your landing page website in less time. You can use the default settings for each template or modify it as per your need.

- [Home Maintenance](https://layoutsfordivibuilder.com/home-maintenance/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Web App](https://layoutsfordivibuilder.com/web-app/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Crezy Yoga](https://layoutsfordivibuilder.com/crezy-yoga/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Education](https://layoutsfordivibuilder.com/education/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Go Hub](https://layoutsfordivibuilder.com/go-hub/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [The Pizzerio](https://layoutsfordivibuilder.com/the-pizzerio/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Web Agency​](https://layoutsfordivibuilder.com/web-agency/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Analytics Landing Page​](https://layoutsfordivibuilder.com/analytics-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Development Agency​](https://layoutsfordivibuilder.com/development-agency/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Education E-learning​](https://layoutsfordivibuilder.com/education-e-learning/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Marketing Landing Page](https://layoutsfordivibuilder.com/marketing-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Portfolio](https://layoutsfordivibuilder.com/portfolio-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Restaurant](https://layoutsfordivibuilder.com/restaurant/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Super Service Mockup](https://layoutsfordivibuilder.com/super-service-mockup/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Vertigo Web Development](https://layoutsfordivibuilder.com/vertigo-web-development/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Fredo Saas Landing Page](https://layoutsfordivibuilder.com/fredo-saas-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Travel Guide](https://layoutsfordivibuilder.com/travel-guide/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Blatropt saas Landing Page](https://layoutsfordivibuilder.com/blatropt-saas-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Resto](https://layoutsfordivibuilder.com/resto/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Nectar Cafe](https://layoutsfordivibuilder.com/nectar-cafe/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Business Corporate](https://layoutsfordivibuilder.com/business-corporate/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Barberica Salon](https://layoutsfordivibuilder.com/barberica-salon/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Coffee Shop](https://layoutsfordivibuilder.com/coffee-shop/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Cleaning Services](https://layoutsfordivibuilder.com/cleaning-services/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Event Jungle](https://layoutsfordivibuilder.com/event-jungle/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Photographer](https://layoutsfordivibuilder.com/photographer/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Bire Real Estate](https://layoutsfordivibuilder.com/bire-real-estate/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Wedding](https://layoutsfordivibuilder.com/wedding/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Gym](https://layoutsfordivibuilder.com/gym/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Spa](https://layoutsfordivibuilder.com/spa/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- [Mobile App Landing Page](https://layoutsfordivibuilder.com/mobile-app-landing-page/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=layoutsfordivi)
- More coming soon!

### Powerfully Ultimate Plugin Features

<strong>1. 30+ Layout Templates</strong>

Select the pre-built layout design from up to 30 templates available.

<strong>2. Default Style and Settings</strong>

Every layout contains a default style and settings with its design—no need to worry about it if you don't have much design knowledge.

<strong>3. Fully Responsive for Any Devices</strong>

It is fully responsive to smartphones, tablets, iPad, laptops, desktop computers, etc.

<strong>4. Cross-browser Compatibility</strong>

All templates are compatible with all major browsers like Firefox, Chrome, Opera, Safari, etc.

<strong>5. Easy To Install, Use, and Customize</strong>

The plugin is user-friendly, with no expertise required. Basic WordPress users can also easily use it.

<strong>6. SEO Friendly</strong>

Layouts for Divi plugin is built with search engine optimization in mind to ensure better rankings across all search engines.

<strong>7. No Coding Skills Required</strong>

You don't need any coding or technical skills to install and use the plugin. It installs just like any other plugin. 

<strong>8. Optimized Performance</strong>

The plugin is built with performance in mind. So, the clean and optimized code ensures a blazing loading speed of the site.

<strong>9. Fast and High Performance</strong>

Well, optimized and lightweight code that won't hurt your server.

### What Makes Layouts for Divi Outstanding?

* It's 100% free for your personal and client's projects.
* Pick from the collections and one-click to import.
* Category-wise filter to choose best for your digital agency.
* Complete your landing page website in less than 5 minutes.
* Clean code, layout, and design.

### Technical Support

We're active for any support issues and feature suggestions. So, I hope you will love it. If you have any more questions, visit our support on the [Plugin's Forum](https://wordpress.org/support/plugin/layouts-for-divi/). Feel free to [contact us](https://www.techeshta.com/contact-us/?utm_medium=wp.org&utm_source=wordpressorg&utm_campaign=readme&utm_content=techeshta) if you want any custom widgets for your site.

<strong>Note:</strong> This plugin is an addon of [Divi Page Builder](https://www.elegantthemes.com/gallery/divi/) and will only work with Divi Page Builder installed.

### Like Layouts for Divi Plugin?

Don't forget to rate us on [WordPress](https://wordpress.org/support/plugin/layouts-for-divi/reviews/?filter=5).

== Installation ==

<strong>From Your WordPress Dashboard</strong>

1. Go to Plugins > <strong>Add New</strong>
2. Search for <strong>Layouts for Divi</strong>
3. Click on the <strong>Install Now</strong> Button
4. Click on <strong>Activate Now</strong> After Installed in the Backend 

<strong>From WordPress.org</strong>

1. Download <strong>[Layouts for Divi](https://downloads.wordpress.org/plugin/layouts-for-divi.zip)</strong>
2. Upload the <strong>'layouts-for-divi'</strong> folder to the /wp-content/plugins/ directory
3. Activate <strong>Layouts for Divi</strong> plugin from your plugins page

== Frequently Asked Questions ==

= For which purposes can I use Layouts for Divi? = 
Layouts for Divi give you top-niche ready-made landing pages or designs for your favorite WordPress Divi Page Builder.

= Can I import these ready-made layouts to my Divi library? = 
 Yes, you can. Layouts for Divi plugin allow you to import any design to the Divi library and pages too!

= Are all the starter templates Free? =
Yes, get over 30+ free ready-to-use websites as of now. You can pick a website template that suits your needs.

= Can I change color or customize imported layouts as per my wish? =
Yes, of course. Why not! You are free to use/modify it as per your project requirements.

= Where can I ask for help? = 
You can reach out via the official [support forum](https://wordpress.org/support/plugin/layouts-for-divi/) on WordPress.org.

== Screenshots ==

1. Layouts for Divi - Backend (Layout Selections)
2. Layouts for Divi - Layout Preview
3. Layouts for Divi - Templates Library list after import layout
4. Layouts for Divi - Install Preview

== Changelog ==

= 1.1.2 =
Release date: April 22nd, 2025

* [Updated] Latest WordPress 6.8 compatibility Check

= 1.1.1 =
Release date: July 26th, 2024

* [Updated] Latest WordPress 6.6.1 compatibility Check

= 1.1 =
Release Date: April 8th, 2024

* Updated: Latest WordPress 6.5 compatibility Check
* Fixed: Security, Performance, and Accessibility issue
* Fixed: Tags listing updated in readme

= 1.0.8 =
Release Date: October 6th, 2023

* Updated: Latest WordPress 6.3.1 compatibility Check

= 1.0.7 =
Release Date: December 8th, 2022

* Updated: Latest WordPress 6.1 compatibility Check

= 1.0.6 =
Release Date: May 25th, 2022

* Updated: Latest WordPress 6.0 compatibility Check

= 1.0.5 =
Release date: August 17th, 2021

* Updated: Latest WordPress 5.8 Compatibility Check

= 1.0.4 =
Release date: March 11th, 2021

* Updated: Latest WordPress 5.7 Compatibility Check

= 1.0.3 =
Release Date: August 16th, 2020

* Updated: Latest WordPress 5.6 Compatibility Check

= 1.0.2 =
Release Date: March 31st, 2020

* Updated: Latest WordPress 5.4 Compatibility Check

= 1.0.1 =
Release Date: November 29th, 2019

* Updated: WordPress 5.3 Compatibility Check

= 1.0 =
Release Date: September 28th, 2019

* Initial release on WordPress.org. Enjoy!
